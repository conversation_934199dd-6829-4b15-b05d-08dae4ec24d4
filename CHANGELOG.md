## [4.0.1](https://git.kicad99.com/bfdx/bf8100-web/compare/v4.0.0...v4.0.1) (2025-09-12)

### Bug Fixes

* add debug job rules ([4fe8d86](https://git.kicad99.com/bfdx/bf8100-web/commit/4fe8d8666fd1e453d998688137d3dfa4b110ac17))
* add tag for debug job ([4d2dd61](https://git.kicad99.com/bfdx/bf8100-web/commit/4d2dd610144cb88457d3edd07ade8c93189cb259))
* add workflow rules ([42d5c56](https://git.kicad99.com/bfdx/bf8100-web/commit/42d5c5608332472417739a27f648cf68f7825962))
* debug stage name ([7650942](https://git.kicad99.com/bfdx/bf8100-web/commit/7650942e61f6433d871d7fc950f9804aede31e31))
* disable workflow ([0fea50e](https://git.kicad99.com/bfdx/bf8100-web/commit/0fea50e40989b3d1cca4f4baad5919f2017fa85a))
* open workflow and stages order ([9d93dba](https://git.kicad99.com/bfdx/bf8100-web/commit/9d93dba629b077bf8b939156db57ac295b56c4a9))
* restore .gitlab-ci.yml all jobs ([20c37b3](https://git.kicad99.com/bfdx/bf8100-web/commit/20c37b3260cea9296560927552c48cc47c5dc0af))
* update  .gitlab-ci.yml ([97ced3e](https://git.kicad99.com/bfdx/bf8100-web/commit/97ced3ee7d3a7d174f841409cf63d20e07ba73ed))
* update ci ([51c9193](https://git.kicad99.com/bfdx/bf8100-web/commit/51c9193bb88eb1e01bed470f45c575e7a22e89d0))
* update rules for release and deploy-build stages ([22dbe53](https://git.kicad99.com/bfdx/bf8100-web/commit/22dbe539cbabb86f1a708f10df536c4416b24920))
* update rules for release stage to allow execution on main branch ([045de94](https://git.kicad99.com/bfdx/bf8100-web/commit/045de94137213fbdea94935c7f2f7e28e5a103cb))
* 地图上快速发送命令组件名称修改 ([d4685e3](https://git.kicad99.com/bfdx/bf8100-web/commit/d4685e365b47818e515736b1523d2f4ed72ff872))

## [4.0.0](https://git.kicad99.com/bfdx/bf8100-web/compare/v3.6.10...v4.0.0) (2025-09-11)

### ⚠ BREAKING CHANGES

* update branches to use 292-8100-ui-v3

### Features

* update branches to use 292-8100-ui-v3 ([cf54e4a](https://git.kicad99.com/bfdx/bf8100-web/commit/cf54e4ae954c64d667cd421635a8873df1ec027e))

### Bug Fixes

* add div wrapper for component, avoiding transition renders non-element root node that cannot be animated ([62959ed](https://git.kicad99.com/bfdx/bf8100-web/commit/62959edc88486df4475c0f2469498f25397ca744))
* 添加login 新ui 界面 ([ce1e286](https://git.kicad99.com/bfdx/bf8100-web/commit/ce1e2864889ffb5338e59ce4949d7ca2471703e7))
* 添加优设标题黑字体 ([098dd89](https://git.kicad99.com/bfdx/bf8100-web/commit/098dd8935f2d1c5a9362a251e10f7682592080da))

## [3.6.10](https://git.kicad99.com/bfdx/bf8100-web/compare/v3.6.9...v3.6.10) (2025-08-25)

### Bug Fixes

* 修复联网通话收到短信通知后不能渲染提示和确定的问题 ([6d3d3ad](https://git.kicad99.com/bfdx/bf8100-web/commit/6d3d3ad54b014295ef417b59193119c6df0778ba))

## [3.6.9](https://git.kicad99.com/bfdx/bf8100-web/compare/v3.6.8...v3.6.9) (2025-07-18)

### Bug Fixes

* 终端、设备、巡逻点编辑数据前判断是否存在编辑权限，动态组编辑前只要有编辑权限或动态组权限就可以编辑数据 ([7a1f906](https://git.kicad99.com/bfdx/bf8100-web/commit/7a1f906070c6018a1715a70eb6a965a6fd0a3dda))
* 请求无权限的单位数据时需要判断rid列表是否为空 ([0da3ea2](https://git.kicad99.com/bfdx/bf8100-web/commit/0da3ea284daff3262a84af853a666c74c738908f))

## [3.6.8](https://git.kicad99.com/bfdx/bf8100-web/compare/v3.6.7...v3.6.8) (2025-07-18)

### Bug Fixes

* 修复无编辑数据权限的用户可以编辑终端，设备和巡逻点管理 ([3a802b0](https://git.kicad99.com/bfdx/bf8100-web/commit/3a802b00afb76f8088cfe7407dd663fa9ea6b9f8))

## [3.6.7](https://git.kicad99.com/bfdx/bf8100-web/compare/v3.6.6...v3.6.7) (2025-07-01)

### Bug Fixes

* 修复历史表传递数据后没有渲染的问题，对短信历史表特别处理（分为短信确认表和未确认表） ([f673c61](https://git.kicad99.com/bfdx/bf8100-web/commit/f673c6138adb8bf236de715c8af481b6abb077ba))
* 短信历史传递一个函数来替换两个表格的data ([48bfcab](https://git.kicad99.com/bfdx/bf8100-web/commit/48bfcab360ece0505611bc60fb66d22d28e65b60))
* 短信历史表判断查询的条件来传递替换表格数据的函数 ([455b2cc](https://git.kicad99.com/bfdx/bf8100-web/commit/455b2cc5f1e617eb0c11c9c7444c67053fd64103))

## [3.6.6](https://git.kicad99.com/bfdx/bf8100-web/compare/v3.6.5...v3.6.6) (2025-06-26)

### Bug Fixes

* build proto ([3ee3258](https://git.kicad99.com/bfdx/bf8100-web/commit/3ee32589e8a40f46cf584be102e97b01a9618213))
* languages.supports使用 Set 以提高性能, 生成support list 时使用set.intersection来取交集 ([0519ddc](https://git.kicad99.com/bfdx/bf8100-web/commit/0519ddc2725472b2b0496e4d4651154a5aa22ae7))
* 优化支持语言列表的生成逻辑, 优先使用config.js配置的语言列表 ([59f3b3f](https://git.kicad99.com/bfdx/bf8100-web/commit/59f3b3f2192cec99e48f5ac701d64d1a09133e6d))
* 使用for...of替代for循环 ([56ef39c](https://git.kicad99.com/bfdx/bf8100-web/commit/56ef39c1efffddc7befac86a89e22180c16ee232))
* 添加允许离线呼叫的选项及相关信息提示 ([78d32e0](https://git.kicad99.com/bfdx/bf8100-web/commit/78d32e02af06ebf254d8e578165f91b17eba2029))

## [3.6.5](https://git.kicad99.com/bfdx/bf8100-web/compare/v3.6.4...v3.6.5) (2025-06-26)

### Bug Fixes

* 修改ci的node镜像为node:20-bullseye ([b7d382e](https://git.kicad99.com/bfdx/bf8100-web/commit/b7d382e691e1267896430de1f85ca7bdb9c6d60c))
* 取消使用docker镜像，linfulong/semantic-release:latest，改在项目中安装依赖，以便升级依赖 ([55c278e](https://git.kicad99.com/bfdx/bf8100-web/commit/55c278e0fe00f9086277ed86baf0572cbdcecf33))

## [3.6.4](https://git.kicad99.com/bfdx/bf8100-web/compare/v3.6.3...v3.6.4) (2025-06-26)


### Bug Fixes

* 优化动态组和组织表格数据变更事件的发布逻辑，确保仅在真实群组时发布事件 ([03679cf](https://git.kicad99.com/bfdx/bf8100-web/commit/03679cfedda527b743411259c8dfe895a20e9362))
* 优化表格行更新和删除逻辑，使用ID选择器直接查找行 ([9868dc3](https://git.kicad99.com/bfdx/bf8100-web/commit/9868dc38d08f78603bc55f01edc0f1a9e8470fcf))
* 使用 getCommonOrgType 来判断单位是真实/虚拟单位。移除未使用的异步函数 ([d647dba](https://git.kicad99.com/bfdx/bf8100-web/commit/d647dbac97d10bdcadca1a7df10725be5dad9c84))
* 使用 nextTick 包裹表格绘制事件的重置高度调用，以确保在 DOM 更新后执行 ([797800b](https://git.kicad99.com/bfdx/bf8100-web/commit/797800bff7ca2624437a852acc42a92f55743e27))
* 修复org在增删改后表格执行了两次draw导致表格又恢复了原来的数据 ([8843e87](https://git.kicad99.com/bfdx/bf8100-web/commit/8843e878afac74defeea3afbfabf201e6b714482))
* 修复标签页切换后，表格高度没有重置正确 ([2f8e787](https://git.kicad99.com/bfdx/bf8100-web/commit/2f8e7876347dbc317c6d66abd1f026873399e984))
* 修复终端和单位页面，getRules使用了防抖导致表单获取不到表单规则 ([8ea831e](https://git.kicad99.com/bfdx/bf8100-web/commit/8ea831e970df65ad4722f595daf5336a23e2855d))
* 修改发布表格数据变更事件的条件，确保仅在存在地图点数据时进行删除操作 ([c51f401](https://git.kicad99.com/bfdx/bf8100-web/commit/c51f40189ba25e1b4454187a2ef8eb0de090a9af))
* 修改表格数据变更事件发布的表名为使用Table后缀 ([890646e](https://git.kicad99.com/bfdx/bf8100-web/commit/890646e0b83c1b6046a335658232f988a12cadbd))
* 修正发布表格事件时的表格名称，确保使用正确的iotDevicesTable ([5b15ab7](https://git.kicad99.com/bfdx/bf8100-web/commit/5b15ab7f387a2b0ba1dc57553ce5486f6299917e))
* 删除不使用的导入 ([75bfc53](https://git.kicad99.com/bfdx/bf8100-web/commit/75bfc532a6494b3d0f059b19f9eebed3bf78ef2b))
* 删除除动态组外的数据表的发布表格增删改行数据 ([68bf53d](https://git.kicad99.com/bfdx/bf8100-web/commit/68bf53de235e5c0e25239b6e978431e4db93bf9e))
* 发布表格数据变更事件，确保数据同步更新 ([ec4a055](https://git.kicad99.com/bfdx/bf8100-web/commit/ec4a055adbc3840df64d3cbd888ad37d5d37248f))
* 将publishTableEvent函数的tableName修改为使用传参 ([1241a2d](https://git.kicad99.com/bfdx/bf8100-web/commit/1241a2d2574cae7fdf2b229002ea623298bfa162))
* 恢复单位管理的代码改动 ([c2cd512](https://git.kicad99.com/bfdx/bf8100-web/commit/c2cd512e7a6364bec63d85b94512de9bf9465931))
* 更新发布表格事件的数据，确保使用最新的数据对象, 收到服务器传递的添加更新动态组通知的时候判断动态组是否存在 ([7651907](https://git.kicad99.com/bfdx/bf8100-web/commit/7651907cfb3b2b3bff741c6bcdc02e4721ebd6e5))
* 更新行数据时添加绘制调用以确保表格正确渲染 ([6c3d35e](https://git.kicad99.com/bfdx/bf8100-web/commit/6c3d35e25d411532735a9e37ed56896d24889d7f))
* 更新巡查点详情确保在发布表格数据变更事件前正确设置数据点 ([48eeed0](https://git.kicad99.com/bfdx/bf8100-web/commit/48eeed0a1b4f695469cf25a09ed3a87e32507b17))
* 添加表格数据变更事件发布功能 ([4aa7180](https://git.kicad99.com/bfdx/bf8100-web/commit/4aa718084971fee3048ffcc6bc029ce7f1473eb9))
* 移除不必要的bfprocess导入，优化代码整洁性 ([bdc4137](https://git.kicad99.com/bfdx/bf8100-web/commit/bdc4137998b1112802004eb13a00e2cfcda85a0b))
* 移除不必要的bfprocess导入，优化代码整洁性 ([9752758](https://git.kicad99.com/bfdx/bf8100-web/commit/97527582156bf0efa0664570d947a9e54b38034c))
* 简化事件名称格式 ([b2dae77](https://git.kicad99.com/bfdx/bf8100-web/commit/b2dae7764e8e0e878c5f699220e95d4c514fe3a9))
* 职位管理没有进行数据同步 ([09b4aeb](https://git.kicad99.com/bfdx/bf8100-web/commit/09b4aeb4b6aec4443e55d3b6ec64a5d4f1e8729f))
* 请求巡查线路详情数据并在成功后发布表格数据变更事件 ([5a11ebd](https://git.kicad99.com/bfdx/bf8100-web/commit/5a11ebd0d568c7401ad4a0397f7bacdb4c879f9f))

## [3.6.3](https://git.kicad99.com/bfdx/bf8100-web/compare/v3.6.2...v3.6.3) (2025-06-09)


### Bug Fixes

* add missing "addArrowImage" function in traceMap.vue ([7585b55](https://git.kicad99.com/bfdx/bf8100-web/commit/7585b55e12da42b7f581e884c601a8de1dc220eb))

## [3.6.2](https://git.kicad99.com/bfdx/bf8100-web/compare/v3.6.1...v3.6.2) (2025-06-09)


### Bug Fixes

* 修复maplibre loadImage用法（回调函数修改为返回一个Promise） ([fcef406](https://git.kicad99.com/bfdx/bf8100-web/commit/fcef40684814e1258592fd5b74eff20770afba5d))
* 修改bfglob.console打印error信息为error ([d3deef4](https://git.kicad99.com/bfdx/bf8100-web/commit/d3deef40684895fcb5bf52cfeaf988e5c861c938))
* 添加loadImage err 处理 ([1b1ed7a](https://git.kicad99.com/bfdx/bf8100-web/commit/1b1ed7a17d1eaa65e174abd0eebb780fdd2d25ee))

## [3.6.1](https://git.kicad99.com/bfdx/bf8100-web/compare/v3.6.0...v3.6.1) (2025-06-06)


### Bug Fixes

* 修复element-plus消息提示的vnode创建错误 ([1c6e039](https://git.kicad99.com/bfdx/bf8100-web/commit/1c6e0396baad234e69fb86ef574a09200f70658e))

## [3.6.0](https://git.kicad99.com/bfdx/bf8100-web/compare/v3.5.9...v3.6.0) (2025-06-05)


### Features

* update prochat device selection to use reactive reference for device info list ([66b034b](https://git.kicad99.com/bfdx/bf8100-web/commit/66b034b34346629ce7b58da5886fcd8d81f67b7c))

## [3.5.9](https://git.kicad99.com/bfdx/bf8100-web/compare/v3.5.8...v3.5.9) (2025-06-05)


### Bug Fixes

* 添加一个用来比较版本号的工具的库 ([a5a58f0](https://git.kicad99.com/bfdx/bf8100-web/commit/a5a58f0e777da6469bccbfe172ab4a414e1f8354))

## [3.5.8](https://git.kicad99.com/bfdx/bf8100-web/compare/v3.5.7...v3.5.8) (2025-05-23)


### Bug Fixes

* delete todo ([8e99f44](https://git.kicad99.com/bfdx/bf8100-web/commit/8e99f444c796d5ef7a3329e70840a4a2f5d5df2a))
* maplibre add maplibregl.addProtocol func ([395b83e](https://git.kicad99.com/bfdx/bf8100-web/commit/395b83e1f76a58b1a687d9ec7b418e62186d1fcf))
* update mapboxgl css class name ([dc1a6f9](https://git.kicad99.com/bfdx/bf8100-web/commit/dc1a6f99b9af65e3bcdd9964da74c23608b23094))
* update mapboxgl js to maplibregl ([7ae4d5e](https://git.kicad99.com/bfdx/bf8100-web/commit/7ae4d5e59a76d573b173bf7cd7515ab1a79952e4))
* 在bfMap、traceMap和BaseMap中使用registerCustomProtocol，由于进入系统主页面就是bfMap,所以需要等待bftk获取完毕后才能initBfMap ([3426a72](https://git.kicad99.com/bfdx/bf8100-web/commit/3426a7254c79b0f46d91f84a3de8f6e39fe55b1f))
* 设置最小服务器版本为2.93.5 ([909178a](https://git.kicad99.com/bfdx/bf8100-web/commit/909178ade81ac4af9ebe60a7bf07128fddb357a1))

## [3.5.7](https://git.kicad99.com/bfdx/bf8100-web/compare/v3.5.6...v3.5.7) (2025-05-23)


### Bug Fixes

* 删除map组件中判断是否为天地图来请求地图数据 ([d57228a](https://git.kicad99.com/bfdx/bf8100-web/commit/d57228ad5a8a69ba10f6eff0e141e0f97c4f9072))
* 更改注释方法 ([b05918c](https://git.kicad99.com/bfdx/bf8100-web/commit/b05918ce08ad4981ae8aee92237478495e5841ec))
* 注释天地图请求数据的方法 ([786801f](https://git.kicad99.com/bfdx/bf8100-web/commit/786801fde7dbd3faa78b350c45cda907be5afcb6))

## [3.5.6](https://git.kicad99.com/bfdx/bf8100-web/compare/v3.5.5...v3.5.6) (2025-05-14)


### Bug Fixes

* 修复BP660/620扫描漫游组列表成员数量默认初始值错误，必须包含一个0xFFFE [release] ([32da8e6](https://git.kicad99.com/bfdx/bf8100-web/commit/32da8e6114d46f2c8c3968f86e42efb94d460830))

## [3.5.5](https://git.kicad99.com/bfdx/bf8100-web/compare/v3.5.4...v3.5.5) (2025-05-13)


### Bug Fixes

* 添加缺失的SK_BT_SEARCH_AUTO_CONNECT按键定义翻译 ([1f1f53c](https://git.kicad99.com/bfdx/bf8100-web/commit/1f1f53c1e4ccddcea7d96d07fae37f046b319ae7))

## [3.5.4](https://git.kicad99.com/bfdx/bf8100-web/compare/v3.5.3...v3.5.4) (2025-05-12)


### Bug Fixes

* BP610是无屏幕的，不需要过滤屏蔽特定的按键定义选项 ([cffcc4c](https://git.kicad99.com/bfdx/bf8100-web/commit/cffcc4ca343372f7f416dfb3b32161ea4f6437e1))
* BP610重新开放编程密码配置项 ([d175a1f](https://git.kicad99.com/bfdx/bf8100-web/commit/d175a1fe3d2f022bd6e27b2f113b6c1e30a10fa1))
* 修复BP860SDC和BP860SVT的录音文件下载功能，添加2字节头信息 ([c6c2c27](https://git.kicad99.com/bfdx/bf8100-web/commit/c6c2c27e53b4619f7c6adc734d8121a03839efbc))
* 修正BP660/610/620机型的录音文件下载功能，添加2字节头信息 ([23b75dd](https://git.kicad99.com/bfdx/bf8100-web/commit/23b75dd3998399bc8b3a9e222e8a848553b4bcb9))

## [3.5.3](https://git.kicad99.com/bfdx/bf8100-web/compare/v3.5.2...v3.5.3) (2025-05-10)


### Bug Fixes

* 修复发送命令窗口打开失败异常，权限检查判断错误 ([4f64bab](https://git.kicad99.com/bfdx/bf8100-web/commit/4f64bab325f6dc3b4a0d1d6b04bce52174ec2cf4))

## [3.5.2](https://git.kicad99.com/bfdx/bf8100-web/compare/v3.5.1...v3.5.2) (2025-05-09)


### Bug Fixes

* 修正路由跳转页面，没有验证相关页面路由权限错误 ([087b451](https://git.kicad99.com/bfdx/bf8100-web/commit/087b4517ff811f93efb1529a493f514067406bc4))
* 添加命令菜单显示配置 ([120e11b](https://git.kicad99.com/bfdx/bf8100-web/commit/120e11b10b82bab38eadddb2c52d3a7e7704d28b))

## [3.5.1](https://git.kicad99.com/bfdx/bf8100-web/compare/v3.5.0...v3.5.1) (2025-05-07)


### Bug Fixes

* 修正录音组件formatTime方法导入路径 [release] ([bec321e](https://git.kicad99.com/bfdx/bf8100-web/commit/bec321e05e2bd8de4e83f30113c994479b75acb2))

## [3.5.0](https://git.kicad99.com/bfdx/bf8100-web/compare/v3.4.11...v3.5.0) (2025-05-07)


### Features

* 以BP660为蓝图创建BF620机型写频页面，处理BP620机型专有功能 ([5913d3a](https://git.kicad99.com/bfdx/bf8100-web/commit/5913d3a4dcec83e8efec46a1cb3f35070ea66f92))
* 以BP660为蓝图创建BP610机型写频页面 ([6e167c2](https://git.kicad99.com/bfdx/bf8100-web/commit/6e167c219391bb51b4a5e4438fc05642e034ea68))


### Bug Fixes

* BP660/620，使用新版录音配置组件 ([d08e6b8](https://git.kicad99.com/bfdx/bf8100-web/commit/d08e6b8f4712ad45abea2b5787fd80d52f7a1438))
* 优化写频功能旧版录音配置组件 ([266d269](https://git.kicad99.com/bfdx/bf8100-web/commit/266d2699feb18a5917f1570862460c73b643ce1d))
* 修正BP660写频部分参数配置逻辑 ([0c77407](https://git.kicad99.com/bfdx/bf8100-web/commit/0c77407751bbfb14636e5590cbcfc75a1e9c8aa1))
* 修正录音列表过滤逻辑中的时间属性引用 ([f3288a2](https://git.kicad99.com/bfdx/bf8100-web/commit/f3288a2a30f482732f73c5808083f88355864e97))
* 修正录音配置组件按目标ID筛选时，目标ID为空时的逻辑错误 ([22ccab5](https://git.kicad99.com/bfdx/bf8100-web/commit/22ccab5ed7e55d87772ab65f0ab2648a27e05f86))
* 修正表单属性auto-complete为autocomplete ([39134d0](https://git.kicad99.com/bfdx/bf8100-web/commit/39134d0089925fa66b266523e7422470b8b3a408))
* 录音列表组件的日期选择器添加默认时间范围 ([06f9c21](https://git.kicad99.com/bfdx/bf8100-web/commit/06f9c21901ab3c5e4283721b506077b76a19d846))
* 提取BP660/610/620录音页面组件 ([7c9f8ba](https://git.kicad99.com/bfdx/bf8100-web/commit/7c9f8ba7ec17bd17f57ccbec1890589ffa79957c))

## [3.4.11](https://git.kicad99.com/bfdx/bf8100-web/compare/v3.4.10...v3.4.11) (2025-04-28)


### Bug Fixes

* 修改BP660按键定义名称，添加按键说明图片预览 ([55d3407](https://git.kicad99.com/bfdx/bf8100-web/commit/55d34075693e8837618db5e5056cbe11865e0423))

## [3.4.10](https://git.kicad99.com/bfdx/bf8100-web/compare/v3.4.9...v3.4.10) (2025-04-27)


### Bug Fixes

* 修复BP660和TD920机型写频页面关于ElSelect组件的ElOption组件key属性应用错误导致页面不断更新引发崩溃错误 [release] ([2c2d1fd](https://git.kicad99.com/bfdx/bf8100-web/commit/2c2d1fd23d4041689c27eff3ab04704e851a251c))

## [3.4.9](https://git.kicad99.com/bfdx/bf8100-web/compare/v3.4.8...v3.4.9) (2025-04-23)


### Bug Fixes

* 修复excel表名太长导致导出数据失败问题 [release] ([8c76d11](https://git.kicad99.com/bfdx/bf8100-web/commit/8c76d1106653a9097b8aa19d1edc794f2f050f00))

## [3.4.8](https://git.kicad99.com/bfdx/bf8100-web/compare/v3.4.7...v3.4.8) (2025-04-16)


### Bug Fixes

* 修改单位管理的上级单位表单为树形下拉列表，且禁用子级单位，并添加上级单位设置成了子级单位的错误消息提示 ([b2d930c](https://git.kicad99.com/bfdx/bf8100-web/commit/b2d930c76eec6f21363d75a18012c7c96cd72170))
* 天地图暂时不支持英文标记 ([c8cabd5](https://git.kicad99.com/bfdx/bf8100-web/commit/c8cabd5adfc4de41b48706b16c62fb51a848597f))

## [3.4.7](https://git.kicad99.com/bfdx/bf8100-web/compare/v3.4.6...v3.4.7) (2025-04-15)


### Bug Fixes

* 使用reactive将state对象转换为响应式对象，以实现动态数据更新和组件状态同步。 ([c95a4d9](https://git.kicad99.com/bfdx/bf8100-web/commit/c95a4d90d2832999d52fc2b934c7a24eaf1d23d9))
* 修正getTiandituMapLang方法i18n语言访问异常 ([a5e1b7a](https://git.kicad99.com/bfdx/bf8100-web/commit/a5e1b7a9386729d083f10c3b15d29c1c76ed5ec8))
* 如果删除的单位是当前登录用户的上级，则提示用户，然后跳转到登录页 ([28a61fd](https://git.kicad99.com/bfdx/bf8100-web/commit/28a61fd1f90b76f89414d10023508bf87a19f57e))

## [3.4.6](https://git.kicad99.com/bfdx/bf8100-web/compare/v3.4.5...v3.4.6) (2025-04-14)


### Bug Fixes

* 修复巡逻点更新时，部分字段时间格式不正确的问题 ([66ce388](https://git.kicad99.com/bfdx/bf8100-web/commit/66ce38848a9c2164c1fb61aaa6f74b223b94b984))

## [3.4.5](https://git.kicad99.com/bfdx/bf8100-web/compare/v3.4.4...v3.4.5) (2025-04-08)


### Bug Fixes

* siteInfo的[@input](https://git.kicad99.com/input)改为[@update](https://git.kicad99.com/update):model-value ([ebd79ab](https://git.kicad99.com/bfdx/bf8100-web/commit/ebd79ab6b9166ba7284390868e41535bd8c8047a))
* TM8250SVT_R7F ui 与写频软件保持一致 ([13ec149](https://git.kicad99.com/bfdx/bf8100-web/commit/13ec14961bb36c4100d7f5597f7cfba072d46edd))
* ui设置的选项使用change方法来同步，不使用watch ([d60c677](https://git.kicad99.com/bfdx/bf8100-web/commit/d60c6778f4e4e1bb83a27a7a7a831b6c9b3081dd))
* 信道配置添加联网模式 ([d18555e](https://git.kicad99.com/bfdx/bf8100-web/commit/d18555e67c7afdc72045f688208b66ac5fc3f36b))
* 删除未使用的导入 ([586c628](https://git.kicad99.com/bfdx/bf8100-web/commit/586c6281d3c6a21ea13b92b67ad0e8eb9e0071cc))
* 完善TM8250SVT_R7F协议内容 ([29c960a](https://git.kicad99.com/bfdx/bf8100-web/commit/29c960a1f85b634cbf31cd47a8eba095464bb910))
* 展示站点信息频率校验失败的结果 ([7e4d28a](https://git.kicad99.com/bfdx/bf8100-web/commit/7e4d28a3262f62f936bee5c5a3331fa726bada67))
* 添加TM8250SVT_R7F的写频组件和协议 ([f8939d5](https://git.kicad99.com/bfdx/bf8100-web/commit/f8939d5462f1b7242af838ff4d516621fe008fb2))
* 添加写入虚拟集群和站点信息的配置 ([324f06a](https://git.kicad99.com/bfdx/bf8100-web/commit/324f06ab396f833e1d6d2b6261c7b9b1bdc77197))
* 菜单设置的选项使用change方法来同步，不使用watch ([4baa450](https://git.kicad99.com/bfdx/bf8100-web/commit/4baa450a5c5cbe283a5c76e7b63df5c3a52c0134))
* 设备信息展示选配功能和ui版本 ([4ba7203](https://git.kicad99.com/bfdx/bf8100-web/commit/4ba7203002962ea316b8f60ba31fb8c5b5dec94a))
* 预制短信添加时赋值短信长度到length属性 ([8ac6256](https://git.kicad99.com/bfdx/bf8100-web/commit/8ac62560475f2fd06aaa4fe2ad8c14f54389dadd))

## [3.4.4](https://git.kicad99.com/bfdx/bf8100-web/compare/v3.4.3...v3.4.4) (2025-04-03)


### Bug Fixes

* 从BF-TD511SDC/SVT复制出来，修改为TD880系列，以解决机型兼容冲突问题 ([a24e01f](https://git.kicad99.com/bfdx/bf8100-web/commit/a24e01f6b88671f4151dd235513df921f982fa6d))
* 对讲机写频设置读、写模式失败时，尝试下发EOT指令 ([6514316](https://git.kicad99.com/bfdx/bf8100-web/commit/65143166aaf00842990fea237a0b66169551f47c))

## [3.4.3](https://git.kicad99.com/bfdx/bf8100-web/compare/v3.4.2...v3.4.3) (2025-04-03)


### Bug Fixes

* 修复511系列蓝牙和录音选配功能默认参数冲突问题 ([3840401](https://git.kicad99.com/bfdx/bf8100-web/commit/3840401421baab00a5ffb2f2b93b80884513307b))

## [3.4.2](https://git.kicad99.com/bfdx/bf8100-web/compare/v3.4.1...v3.4.2) (2025-04-03)


### Bug Fixes

* 修复对讲机写频导入的tailwindcss样式异常 ([73dfc6c](https://git.kicad99.com/bfdx/bf8100-web/commit/73dfc6cf027fb61267d4d103a2945146342ba33f))

## [3.4.1](https://git.kicad99.com/bfdx/bf8100-web/compare/v3.4.0...v3.4.1) (2025-04-03)


### Bug Fixes

* 路由使用 hash 模式，兼容性更好 ([1ed2936](https://git.kicad99.com/bfdx/bf8100-web/commit/1ed2936b724aa6670501d8c4d51b9270c7a93883))
* 重命名根目录下的nodejs脚本，使用cjs后缀名以便让node直接执行 ([3fed343](https://git.kicad99.com/bfdx/bf8100-web/commit/3fed3435cfccea5d82387dffb3ca5f2f7e86cfaa))

## [3.4.0](https://git.kicad99.com/bfdx/bf8100-web/compare/v3.3.15...v3.4.0) (2025-04-03)


### Features

* 完成 vue3+element-plus升级 ([a6b89b2](https://git.kicad99.com/bfdx/bf8100-web/commit/a6b89b239ca19df1ffc005f7ad4a9ed82ff46a04))


### Bug Fixes

* "位置终端授权el-select"添加"请选择"和"无匹配数据"的占位符 ([c6cbfe6](https://git.kicad99.com/bfdx/bf8100-web/commit/c6cbfe691c9661049b5539d8be905a144fec8d90))
* 860写频-漫游设置label位置设置为top ([34a72e7](https://git.kicad99.com/bfdx/bf8100-web/commit/34a72e75690bfaa81395bd9374f035953fcb4335))
* bcxx查询弹窗销毁删除this.$destroy() ([c3d0578](https://git.kicad99.com/bfdx/bf8100-web/commit/c3d0578d059856ecee7c47c269f8306efe303363))
* bf-input-number调整高度跟input组件一致 ([183f56b](https://git.kicad99.com/bfdx/bf8100-web/commit/183f56b41ceef8a9f273e310050487fba0b4faf4))
* bfSpeaking删除data中qWebChannelObj定义 ([0fa5765](https://git.kicad99.com/bfdx/bf8100-web/commit/0fa57652eb30a5d96bece1f63e0dcedc9b40f7b2))
* el-transfer中传递的key为" value", 导致出现el-checkbox的label现在应该为value的警告，删除前面的空格, 传递"value" ([ea5616d](https://git.kicad99.com/bfdx/bf8100-web/commit/ea5616d27a8cd71e6d34e7e62bcde554c5a67f8e))
* form-item下面第一个且为input-number的元素，宽度为100% ([2fa8bd2](https://git.kicad99.com/bfdx/bf8100-web/commit/2fa8bd224b20c33022e7c40f4dcc189c7737793f))
* icon "info" -> "InfoFilled" ([078fa58](https://git.kicad99.com/bfdx/bf8100-web/commit/078fa589df12eb4240d893d1c000edcb72cc3095))
* icon使用element+的el-icon ([e4e44dc](https://git.kicad99.com/bfdx/bf8100-web/commit/e4e44dc5693a4b8d637683dbb709e3eb1cbc2691))
* QWebChannel使用reactive定义为响应式 ([0b92917](https://git.kicad99.com/bfdx/bf8100-web/commit/0b92917bcc4561927376538b64c5b579c6cdeab0))
* tableTree在挂载玩后执行一次updateViewport来重置树形表格高度 ([bc36d17](https://git.kicad99.com/bfdx/bf8100-web/commit/bc36d176d9d5e619c13b82ef0164028384b0a260))
* TR925DbContact和TR925DmrContact使用拖拽自定义指令 ([2a8540f](https://git.kicad99.com/bfdx/bf8100-web/commit/2a8540f7fdecdc031b1ab2a42153d43c796b3361))
* treeTable执行getViewportCount访问$refs.fancytreeGridContainer为null时，直接返回0, getViewportCount调用处检查为0时都会重新调用 ([4b1d104](https://git.kicad99.com/bfdx/bf8100-web/commit/4b1d10418aa25050acd62dfead071cc2aa7f76dd))
* UI设置的布局优化，按键定义默认数据修改 ([61d1425](https://git.kicad99.com/bfdx/bf8100-web/commit/61d14252555a57953f22fe9721a3abefb122c19a))
* ui设置的布局修改 ([3e8f111](https://git.kicad99.com/bfdx/bf8100-web/commit/3e8f111551da9d98a1baf3f42ae8ced29581e41f))
* 信道区域展示组件props参数错误 ([fddc493](https://git.kicad99.com/bfdx/bf8100-web/commit/fddc493e6895bd1c197d4c7035eac8c5b48ca1ae))
* 修复树形高度在没有搜索框时计算错误 ([6a75bf0](https://git.kicad99.com/bfdx/bf8100-web/commit/6a75bf0cd3166056c1eaf5b97c02a809b04f02df))
* 修复终端更新时，虚拟单位没有正常设置异常 ([9dd8686](https://git.kicad99.com/bfdx/bf8100-web/commit/9dd86864dea00d3bccaed67afea76cecd1c308a1))
* 修改el-popover的使用方法 ([e73e8d8](https://git.kicad99.com/bfdx/bf8100-web/commit/e73e8d8b8e0bf22a9641d4eafc1ca95c1a398769))
* 写频页面-信令系统-数字紧急警报列表样式修改 ([d842406](https://git.kicad99.com/bfdx/bf8100-web/commit/d842406cd1747e8c28220832e6340450c4a27700))
* 写频页面为tab标签页的扫描和漫游添加h-full ([4c6fe71](https://git.kicad99.com/bfdx/bf8100-web/commit/4c6fe71deace1b7b0e5d8f5eddc883d6b9deafac))
* 写频页面的样式el-tabs的value修改为model-value ([297a44b](https://git.kicad99.com/bfdx/bf8100-web/commit/297a44b1e7d576d14749974c043847ca665d757a))
* 写频页面的样式full-height更改为h-full ([94596ae](https://git.kicad99.com/bfdx/bf8100-web/commit/94596aec14a31f4475786363e95f786bf94c2920))
* 写频页面的样式扫描和漫游列表选项样式处理 ([e7616e7](https://git.kicad99.com/bfdx/bf8100-web/commit/e7616e76c5930d6d1fde920beeaaae4e32db5d1f))
* 发送命令的section的边距调整 ([d738d8a](https://git.kicad99.com/bfdx/bf8100-web/commit/d738d8a533a9f849ae1135273d9ea8c192d7ea16))
* 合并主分支，解决合并冲突 ([68e25ff](https://git.kicad99.com/bfdx/bf8100-web/commit/68e25fff15f898b0a00cc0421e715bf152eb694d))
* 合并最新的可靠定位模式分支 ([7317093](https://git.kicad99.com/bfdx/bf8100-web/commit/7317093792ead4e9ff769b160fb8b65ca40dcd3a))
* 在element的公共样式中添加对拖拽弹窗的公共样式 ([62e4609](https://git.kicad99.com/bfdx/bf8100-web/commit/62e4609dedeecaf56b5d5f9136bf969edfd0c28a))
* 在报警弹窗和联网通话应用拖拽指令 ([fec05cb](https://git.kicad99.com/bfdx/bf8100-web/commit/fec05cba578faadf73baf7c4292eba3d403f9c34))
* 在网络对讲终端请求终端位置信息的弹窗应用drag指令 ([bc7320d](https://git.kicad99.com/bfdx/bf8100-web/commit/bc7320d42f021256a1926d66c3b01d381d639324))
* 对讲机写频录音页面的按钮组样式添加 ([d33e4a1](https://git.kicad99.com/bfdx/bf8100-web/commit/d33e4a1540c188f08f5fc87ae84c6b90fba4edc4))
* 将自定义拖拽指令独立出来并使用 ([15032d0](https://git.kicad99.com/bfdx/bf8100-web/commit/15032d0c4fc3c61b7e79ed9cab5294a167afd8fd))
* 打开编辑框后，将非虚拟点的gpsPointRadius重置为0 ([816e3d3](https://git.kicad99.com/bfdx/bf8100-web/commit/816e3d309ce7fbe42494336f5fbf63b32d6bfab3))
* 报警提时，自定义指令的update没有执行，添加“bf-drag”class放在mounted中执行示弹窗 ([2cf015c](https://git.kicad99.com/bfdx/bf8100-web/commit/2cf015cce1bbbc7e7515a1171b709de3c8c90720))
* 数据类窗口头部ui移动端调整 ([062f813](https://git.kicad99.com/bfdx/bf8100-web/commit/062f8134df228c281dc949613f45010db9ba27fa))
* 查询类和数据管理类组件移动端适配 ([8bb7f93](https://git.kicad99.com/bfdx/bf8100-web/commit/8bb7f937bdb4f0705cdd24be59a51a299db02187))
* 树形列表写频组件-加密配置头部统一在tabsWf.scss定义 ([c50d424](https://git.kicad99.com/bfdx/bf8100-web/commit/c50d424c8a403f2c58b6386903b2cf8ae4b421ee))
* 树形列表写频组件的css代码统一编写在tabsWf和tree-card-layout中 ([2fcaaf6](https://git.kicad99.com/bfdx/bf8100-web/commit/2fcaaf685c23aed83624db91d7930f7b93e92996))
* 树形结构写频组件穿梭框限制高度 ([5876393](https://git.kicad99.com/bfdx/bf8100-web/commit/5876393f077788d8184882934ace48077b331f3e))
* 根目录添加jsconfig.json配置 ([1b035bf](https://git.kicad99.com/bfdx/bf8100-web/commit/1b035bf800a724941a2d0857de280c90ad9011ee))
* 网络对讲终端请求终端位置信息在dialog上移除原有的v-bfdirective.drag ([70d57f1](https://git.kicad99.com/bfdx/bf8100-web/commit/70d57f11be2cd0592f46a14b6ec16d7089771919))
* 自定义指令使用el-dialog自带的拖拽 ([3479093](https://git.kicad99.com/bfdx/bf8100-web/commit/3479093190776e4c3eabaac1a6365b359aa6cdeb))
* 设置generateDmrId的宽度为100% ([e65d6d9](https://git.kicad99.com/bfdx/bf8100-web/commit/e65d6d95bf46323357f9ad02a4a8f09c63850fec))
* 设置联网通话dialog父级div高度为0，不遮挡地图的拖动 ([8ae8503](https://git.kicad99.com/bfdx/bf8100-web/commit/8ae85033c0e9eb9e11b4130cd6d15d4618b43f8d))
* 调整写频中输入框和label的位置、电话簿的表格高度 ([1872466](https://git.kicad99.com/bfdx/bf8100-web/commit/18724661cb2c206c3630335c8fa9e744bef5d5c3))
* 调整写频页面宽度和部分ui展示 ([b9764ea](https://git.kicad99.com/bfdx/bf8100-web/commit/b9764eac3d45ecfe3190f30d62a4654aa91c7de9))
* 调整发送命令页面的数字输入框的宽度 ([9ef4768](https://git.kicad99.com/bfdx/bf8100-web/commit/9ef47686e1a21c95dc9cc47df7c88b9fde92bfe1))
* 调整发送命令页面的表单的size ([a609c60](https://git.kicad99.com/bfdx/bf8100-web/commit/a609c6070f8b58afddee82a7ffdc0faeea2ec695))
* 调整查询类页面表头和表尾移动端ui适配 ([0713736](https://git.kicad99.com/bfdx/bf8100-web/commit/0713736cd68210eb5cd95538ed4ae3a31d4c14aa))
* 调整树形列表写频组件的样式 ([6d75baf](https://git.kicad99.com/bfdx/bf8100-web/commit/6d75bafd87ad5fa7bf667cf6e2e46aa82aacb685))
* 调整移动端导航栏样式 ([06880bd](https://git.kicad99.com/bfdx/bf8100-web/commit/06880bd48ac897624476ce4a0062c3e256d3b683))
* 调整联网通话的样式 ([ac3df50](https://git.kicad99.com/bfdx/bf8100-web/commit/ac3df50bc79bb2af8ddbfdff9efdb79b77bfd827))
* 调整自定义指令的元素获取，在联网通话中使用拖拽自定义指令 ([112a03a](https://git.kicad99.com/bfdx/bf8100-web/commit/112a03a29ee7f3c70bb20111461942796ae05d49))
* 调整通讯录table的高度， 删除加密配置组件的操作按钮列的:key="Date.now()" ([2442296](https://git.kicad99.com/bfdx/bf8100-web/commit/2442296373278a4a3f6563b32cf543f61f932296))
* 频率偏移的映射方法直接在emits定义更新的方法 ([447d33b](https://git.kicad99.com/bfdx/bf8100-web/commit/447d33bb5cf7912b44d74d6093f2cf4678603df8))

## [3.3.15](https://git.kicad99.com/bfdx/bf8100-web/compare/v3.3.14...v3.3.15) (2025-04-01)


### Bug Fixes

* 修改可靠定位传输的功能描述 ([22f939d](https://git.kicad99.com/bfdx/bf8100-web/commit/22f939db47825dcf0d61e1df24847b60b722de50))

## [3.3.14](https://git.kicad99.com/bfdx/bf8100-web/compare/v3.3.13...v3.3.14) (2025-03-28)


### Bug Fixes

* DZ148SVT的虚拟集群中继状态信息默认为正常，自定义功率最小为5 ([f5e2118](https://git.kicad99.com/bfdx/bf8100-web/commit/f5e2118455daf208318c276226953a4a33739694))
* 将虚拟集群中继定义到根目录的modelInfo中 ([3a2624a](https://git.kicad99.com/bfdx/bf8100-web/commit/3a2624a18bc13fff3964673477827a330d624960))
* 添加DZ148SVT的虚拟集群列表中继型号所展示的名称 ([65f3eb4](https://git.kicad99.com/bfdx/bf8100-web/commit/65f3eb43a1079e7c83f47e33f0a22035fd6dbf1b))

## [3.3.13](https://git.kicad99.com/bfdx/bf8100-web/compare/v3.3.12...v3.3.13) (2025-03-26)


### Bug Fixes

* 更新中继型号BR1050M名称为BR1050（SDC） ([b11ec2b](https://git.kicad99.com/bfdx/bf8100-web/commit/b11ec2ba849fc1e8b49a72aa448d637a85b35299))

## [3.3.12](https://git.kicad99.com/bfdx/bf8100-web/compare/v3.3.11...v3.3.12) (2025-03-24)


### Bug Fixes

* 修正设备管理中继状态监控窗口，打开页面时没有显示数据更新时间问题 ([08cb5a2](https://git.kicad99.com/bfdx/bf8100-web/commit/08cb5a2713f97f3f0b383c487371efe5e7466c92))

## [3.3.11](https://git.kicad99.com/bfdx/bf8100-web/compare/v3.3.10...v3.3.11) (2025-03-24)


### Bug Fixes

* 中继写频的BR1050M和TR8500M型号的切换信道限制最大值99个 ([12eabe5](https://git.kicad99.com/bfdx/bf8100-web/commit/12eabe53605195a45fec38c3e548db6f035e83a8))
* 添加注释解释最大信道数 ([4898ec0](https://git.kicad99.com/bfdx/bf8100-web/commit/4898ec045a8e9f087cb9b48cda634f65e8604d0d))

## [3.3.10](https://git.kicad99.com/bfdx/bf8100-web/compare/v3.3.9...v3.3.10) (2025-03-20)


### Bug Fixes

* vite.config.js添加transformMixedEsModules配置，处理js导入文件和方法识别不到 ([b78028a](https://git.kicad99.com/bfdx/bf8100-web/commit/b78028a53ed5ac0f7124d6a72cfda4713d828d92))
* 删除vite.config.js的transformMixedEsModules配置 ([66c8142](https://git.kicad99.com/bfdx/bf8100-web/commit/66c814274cbb0464fa1b1b1dc48278f669489804))
* 添加BR1050M中继机型写频，机型码BR105M ([098d6c6](https://git.kicad99.com/bfdx/bf8100-web/commit/098d6c6b8b7b6dfd4ab91adace72f52a8ea001a1))

## [3.3.9](https://git.kicad99.com/bfdx/bf8100-web/compare/v3.3.8...v3.3.9) (2025-03-14)


### Bug Fixes

* 恢复合并时的版本3.3.8 ([b3b5e74](https://git.kicad99.com/bfdx/bf8100-web/commit/b3b5e741cc05ebba9344a709e18bf1af4e19dd06))
* 服务器最小版本设置为2.92.91 ([391f38b](https://git.kicad99.com/bfdx/bf8100-web/commit/391f38b8f3e7932df81ec2f6ee4f60d55d25ba2d))

## [3.3.8](https://git.kicad99.com/bfdx/bf8100-web/compare/v3.3.7...v3.3.8) (2025-03-14)


### Bug Fixes

* 可靠定位模式修改为可靠定位传输，勾选项旁边加上icon,hover是解释该选项的功能 ([682681e](https://git.kicad99.com/bfdx/bf8100-web/commit/682681eb34e35a6bbfe7698ce9595b63fb1d2c6f))
* 调整icon颜色为灰色 ([d21da97](https://git.kicad99.com/bfdx/bf8100-web/commit/d21da978b7de1fe47932df9afddb0f4d1917c118))

## [3.3.7](https://git.kicad99.com/bfdx/bf8100-web/compare/v3.3.6...v3.3.7) (2025-03-13)


### Bug Fixes

* gitlab-ci恢复lint部分 ([06a5beb](https://git.kicad99.com/bfdx/bf8100-web/commit/06a5beb92b2184559e7fa1122bc03e043597d832))
* rgps默认为不勾选，在执行添加或更新的方法时同步rgpsMode ([a5ad3cb](https://git.kicad99.com/bfdx/bf8100-web/commit/a5ad3cbe62e66d977592e7d37d3cee6e1cd74a09))
* update bf8100.proto and build proto ([e7018a9](https://git.kicad99.com/bfdx/bf8100-web/commit/e7018a983d4e60a33d90ac30ab4e942035d7c03a))
* 在新增或切换终端类型的时候判断是否为poc或网络对讲终端，添加上rgps字段 ([c8fbb0c](https://git.kicad99.com/bfdx/bf8100-web/commit/c8fbb0c0a87f42230f3cca7bb1b3415e9efdf520))
* 更新poc和网络对讲终端的时候，添加rgps字段，poc同时添加pocConfig的rgps字段 ([6ea2f4b](https://git.kicad99.com/bfdx/bf8100-web/commit/6ea2f4b01442f7081473c0d03189596fa7c1711f))
* 更新rgps的时候，需要更新pocSettingLastModifyTime ([ded03be](https://git.kicad99.com/bfdx/bf8100-web/commit/ded03be74e96a39c490893813da8a5f0daa9691b))
* 添加"可靠定位模式"，新增的时候添加默认添加"可靠定位模式"为1 ([eeaabb4](https://git.kicad99.com/bfdx/bf8100-web/commit/eeaabb4d90668190dfb67716b93adeae730e627e))
* 添加时同步setting的rgps ([ea531ba](https://git.kicad99.com/bfdx/bf8100-web/commit/ea531ba5a1cfe5ed7532d0b4fdd65fe7f4ad074b))

## [3.3.6](https://git.kicad99.com/bfdx/bf8100-web/compare/v3.3.5...v3.3.6) (2025-02-26)


### Bug Fixes

* 修正设备管理中继状态按钮事件绑定失败问题 ([95352ed](https://git.kicad99.com/bfdx/bf8100-web/commit/95352ed54a5084a9bcbcbaf36348d473740e8bae))

## [3.3.5](https://git.kicad99.com/bfdx/bf8100-web/compare/v3.3.4...v3.3.5) (2025-02-17)


### Bug Fixes

* bc13指令也执行重新过滤在线设备事件 ([3e4ef29](https://git.kicad99.com/bfdx/bf8100-web/commit/3e4ef29eed41fc58e1584f5cc71b37f114dbb30a))
* 修正setModelOpacity判断".v-modal"元素是否存在 [release] ([3415a35](https://git.kicad99.com/bfdx/bf8100-web/commit/3415a35a586691978a87a247d8e610847836786a))

## [3.3.4](https://git.kicad99.com/bfdx/bf8100-web/compare/v3.3.3...v3.3.4) (2025-02-14)


### Bug Fixes

* 修复DataFormEditor.vue组件查找表单组件方法在递归时修改了vue实例$children导致数据更新几次后查找失败异常[release] ([7711817](https://git.kicad99.com/bfdx/bf8100-web/commit/771181705dde153ab951f68db51df39fb84c78ca))

## [3.3.3](https://git.kicad99.com/bfdx/bf8100-web/compare/v3.3.2...v3.3.3) (2025-02-13)


### Bug Fixes

* 修正audio的src属性空值时，导致出现"ReferenceError: require is not defined"错误问题 ([f02ff1c](https://git.kicad99.com/bfdx/bf8100-web/commit/f02ff1c36bc7a09cc19ebc841956ed61ee2d47b9))
* 降级vite版本为5.4.14,以解决打包后el-table内容无法正常渲染异常 [release] ([6582ae4](https://git.kicad99.com/bfdx/bf8100-web/commit/6582ae4f94820c16ba98fc39c730f39bd5bc1573))

## [3.3.2](https://git.kicad99.com/bfdx/bf8100-web/compare/v3.3.1...v3.3.2) (2025-02-13)


### Bug Fixes

* 取消artifacts变量配置 [release] ([e6b4896](https://git.kicad99.com/bfdx/bf8100-web/commit/e6b4896fb547058d07341785dc13f75c759b0eb4))

## [3.3.1](https://git.kicad99.com/bfdx/bf8100-web/compare/v3.3.0...v3.3.1) (2025-02-13)


### Bug Fixes

* 修改ci node镜像版本为18 ([fa6074f](https://git.kicad99.com/bfdx/bf8100-web/commit/fa6074f451a2584c5492c1403179a0776f2c8ea2))

## [3.3.0](https://git.kicad99.com/bfdx/bf8100-web/compare/v3.2.17...v3.3.0) (2025-02-13)


### Features

* 完成vue-cli迁移到vite, vue2+element-ui [release] ([33c6a8e](https://git.kicad99.com/bfdx/bf8100-web/commit/33c6a8e6f99f44d2dd99a3eea15c821552b1d999))

## [3.2.17](https://git.kicad99.com/bfdx/bf8100-web/compare/v3.2.16...v3.2.17) (2025-02-07)


### Bug Fixes

* delete option renderNode ([9147c0e](https://git.kicad99.com/bfdx/bf8100-web/commit/9147c0e88aa37e77cf882adf3bd53e3ef637709e))
* poc通讯录列表滚动宽度自适应 ([a5b2b53](https://git.kicad99.com/bfdx/bf8100-web/commit/a5b2b53cefab8a6ab6348917b388998e6f191887))
* 删除传递的参数 ([8a6e34e](https://git.kicad99.com/bfdx/bf8100-web/commit/8a6e34e506aa83a5ed78ea34ecba43ff17c652c9))
* 在渲染前使用fancytree内置的方法忽略符合要求的节点 ([8c828a6](https://git.kicad99.com/bfdx/bf8100-web/commit/8c828a63c73465809959d320102c35c7ed63f133))
* 获取动态组，在通讯录选择中删除动态组，动态组不能可选 ([06deb9a](https://git.kicad99.com/bfdx/bf8100-web/commit/06deb9a4d6b0d49e6e6c38fa36466b9b3eda9c6d))

## [3.2.16](https://git.kicad99.com/bfdx/bf8100-web/compare/v3.2.15...v3.2.16) (2024-12-25)


### Bug Fixes

* 修复终端的信道管理，默认发射组为上级单位 ([f9376d8](https://git.kicad99.com/bfdx/bf8100-web/commit/f9376d8a2fda87b6dc7fa734c8cde957d7ae4ca1))

## [3.2.15](https://git.kicad99.com/bfdx/bf8100-web/compare/v3.2.14...v3.2.15) (2024-11-27)


### Bug Fixes

* bc12开机指令添加对av!==4的判断 ([fef3f28](https://git.kicad99.com/bfdx/bf8100-web/commit/fef3f2846454d763d30f2751354b8da00fadf700))
* 锁定服务器最小版本 [release] ([758c51d](https://git.kicad99.com/bfdx/bf8100-web/commit/758c51d992f3836ef7bfbb7707c57ce9bb7e602c))

## [3.2.14](https://git.kicad99.com/bfdx/bf8100-web/compare/v3.2.13...v3.2.14) (2024-11-27)


### Bug Fixes

* 取消bc12开机指令对定位av值参数处理，统一update_device_gps方法处理 ([25e50d5](https://git.kicad99.com/bfdx/bf8100-web/commit/25e50d5e4ca457a3ff6b26da40f46c78ff255c85))

## [3.2.13](https://git.kicad99.com/bfdx/bf8100-web/compare/v3.2.12...v3.2.13) (2024-11-27)


### Bug Fixes

* 服务器模拟定位指令，以让特定设备(如带插话功能中继)保活，忽略不处理 ([d51c6ab](https://git.kicad99.com/bfdx/bf8100-web/commit/d51c6ab4aca89d9527f9a9e9ef97abe6ff8b586a))

## [3.2.12](https://git.kicad99.com/bfdx/bf8100-web/compare/v3.2.11...v3.2.12) (2024-11-26)


### Bug Fixes

* 开放中继虚拟终端树形右键命令类菜单 ([5b6390b](https://git.kicad99.com/bfdx/bf8100-web/commit/5b6390be4dfcdff4c3b003e1ad5b4958177af67d))

## [3.2.11](https://git.kicad99.com/bfdx/bf8100-web/compare/v3.2.10...v3.2.11) (2024-11-26)


### Bug Fixes

* fix: fixed issues 275，修复datatables状态管理的length异常导致渲染数据不齐全 ([1dbd446](https://git.kicad99.com/bfdx/bf8100-web/commit/1dbd446cb2731f7f59749cb621bbf5a80a9a4e00))

## [3.2.10](https://git.kicad99.com/bfdx/bf8100-web/compare/v3.2.9...v3.2.10) (2024-11-25)


### Bug Fixes

* 开放中继虚拟终端允许发送命令 ([16ed5a6](https://git.kicad99.com/bfdx/bf8100-web/commit/16ed5a659a3af9fe1bf7c92908a83a234cc5c045))

## [3.2.9](https://git.kicad99.com/bfdx/bf8100-web/compare/v3.2.8...v3.2.9) (2024-11-25)


### Bug Fixes

* ui设置新增肩咪警报 ([d146a49](https://git.kicad99.com/bfdx/bf8100-web/commit/d146a491ff2361658a17504431a8010ee0ee3d35))
* 信道发射功率新中功率选择 ([4050a9d](https://git.kicad99.com/bfdx/bf8100-web/commit/4050a9d7764a686a58c9b81b88a1c44d5b73d851))
* 菜单设置->设备信息中新增生产信息 ([714afd1](https://git.kicad99.com/bfdx/bf8100-web/commit/714afd1fc9b28a6ecc90dd4f058f60745e0bd911))
* 选配功能添加振动 ([9098a8f](https://git.kicad99.com/bfdx/bf8100-web/commit/9098a8f9e3c8bdfb2f625c67b720a5360420d2a5))

## [3.2.8](https://git.kicad99.com/bfdx/bf8100-web/compare/v3.2.7...v3.2.8) (2024-11-15)


### Bug Fixes

* 510svt协议机型码处理、按键定义、常规设置添加蓝牙相关，按键定义使用协议2 ([8f1c68c](https://git.kicad99.com/bfdx/bf8100-web/commit/8f1c68c5f6c8d933239df84278be356c354bcaec))
* 协议注释修改 ([02cfe4e](https://git.kicad99.com/bfdx/bf8100-web/commit/02cfe4e39d364e528eeeffed0632903365834bc9))
* 按键定义屏蔽部分不支持的按键功能,按键功能根据机器选配功能提供录音开关还是蓝牙开关 ([c61f92b](https://git.kicad99.com/bfdx/bf8100-web/commit/c61f92bd5eafb2293332f0c360696bd0d37ca9c2))
* 根据是否是生产环境判断devtool的设置 ([1b63958](https://git.kicad99.com/bfdx/bf8100-web/commit/1b63958e08a76d88069099524723b4976bfcb4e4))
* 设备信息添加选配功能，常规设置添加蓝牙相关设置，根据选配显示蓝牙还是录音 ([d29d73b](https://git.kicad99.com/bfdx/bf8100-web/commit/d29d73b28083f2984f4698a8c46480dfff6b9dcf))

## [3.2.7](https://git.kicad99.com/bfdx/bf8100-web/compare/v3.2.6...v3.2.7) (2024-11-15)


### Bug Fixes

* pocSetting默认属性中添加一个pocConfig字段 ([f9e49f4](https://git.kicad99.com/bfdx/bf8100-web/commit/f9e49f4aaddf5c2f656a67b5bdfd2ce5955c7818))
* 协议更新 ([2810414](https://git.kicad99.com/bfdx/bf8100-web/commit/281041480c2d164257ab0d6e85bb488651f50dcc))
* 协议更新 ([e8a6872](https://git.kicad99.com/bfdx/bf8100-web/commit/e8a6872281043738362af09714341358d51b89a3))
* 本地编辑收听组的允许为1,拒绝为0 ([b327061](https://git.kicad99.com/bfdx/bf8100-web/commit/b32706154373fefb476f6b656b6c979507e89341))
* 本地编辑收听组默认不选中 ([611891a](https://git.kicad99.com/bfdx/bf8100-web/commit/611891a347663972f1835881b4ddbe747a7ae184))
* 本地编辑收听组默认为不允许,添加PocConfig协议 ([fccf10d](https://git.kicad99.com/bfdx/bf8100-web/commit/fccf10d0785d0a37c44ac62b61e254358a7f958e))
* 格式化语言包文件 ([eb79362](https://git.kicad99.com/bfdx/bf8100-web/commit/eb79362e7da98cedd0dbe88556c1dab82e6921b8))
* 注释 ([9d7e10b](https://git.kicad99.com/bfdx/bf8100-web/commit/9d7e10b57d9866996fc2c095ec04bf718050c087))
* 添加国际化翻译 ([aead269](https://git.kicad99.com/bfdx/bf8100-web/commit/aead269cf33056269a62ba9b01ce79fa5cdc184a))

## [3.2.6](https://git.kicad99.com/bfdx/bf8100-web/compare/v3.2.5...v3.2.6) (2024-11-01)


### Bug Fixes

* mounted中initPocSetting与restoreNodeSelectedStatus数据执行顺序修改 ([3b77c58](https://git.kicad99.com/bfdx/bf8100-web/commit/3b77c5883250953d67325df5f112480780ac21dc))
* 修复poc终端通讯录勾选最大值后,退出后无法渲染通讯录节点的问题 ([318e31d](https://git.kicad99.com/bfdx/bf8100-web/commit/318e31de87fbbbf3d28f714cfc8475635ee96c7e))

## [3.2.5](https://git.kicad99.com/bfdx/bf8100-web/compare/v3.2.4...v3.2.5) (2024-10-31)


### Bug Fixes

* poc终端添加失败后,需要将poc密码重置为原始密码 ([fe04f43](https://git.kicad99.com/bfdx/bf8100-web/commit/fe04f43ffc43faa5a9ef129ef6c8ac145c30cc15))
* poc终端添加成功后,可继续使用上一次成功添加的poc配置添加 ([ec3a231](https://git.kicad99.com/bfdx/bf8100-web/commit/ec3a231356286db9ea2c63a17b4800347294562b))
* 删除不使用的导入 ([9ff7290](https://git.kicad99.com/bfdx/bf8100-web/commit/9ff72907d3adfc7163c779cacfc5712bbbf727a4))
* 工作状态显示为对讲机 ([e7639f7](https://git.kicad99.com/bfdx/bf8100-web/commit/e7639f76e7a409e1360e7fc578c730b0c795993f))
* 终端类型选择poc终端时,将formData赋值一次给editRow; poc终端配置管理初始化时,可能会出现响应式对象没有更新的bug,使用this.$set更新pocSettingData ([f6b888c](https://git.kicad99.com/bfdx/bf8100-web/commit/f6b888c09c79357e7c805b3d573f10dd837566b5))
* 虚拟集群所属归属组默认值为动态归属组('') ([25228db](https://git.kicad99.com/bfdx/bf8100-web/commit/25228dbba0dd0d2cdacff876f785253bc8955596))
* 通讯录节点选择函数可能出现数据还未初始化完成就执行的可能,需要等待50ms后在执行 ([6060872](https://git.kicad99.com/bfdx/bf8100-web/commit/606087261acb34fdf8680a9cebf64f1f7747c4f1))
* 首页设备树中的poc终端也可展示设备状态等 ([d73734e](https://git.kicad99.com/bfdx/bf8100-web/commit/d73734e9acc277f235eb595d97dea40342a7f99b))

## [3.2.4](https://git.kicad99.com/bfdx/bf8100-web/compare/v3.2.3...v3.2.4) (2024-10-25)


### Bug Fixes

* 修复设备管理添加设备异常问题 ([f749141](https://git.kicad99.com/bfdx/bf8100-web/commit/f7491418fa6dc7d7e0915bc5c47d27fe9b02a618))

## [3.2.3](https://git.kicad99.com/bfdx/bf8100-web/compare/v3.2.2...v3.2.3) (2024-10-24)


### Bug Fixes

* dataTable状态使用自动存储，存储到sessionStorage ([696bcea](https://git.kicad99.com/bfdx/bf8100-web/commit/696bceac5e078348c34ae3be830dc71daf50a0b1))
* poc终端联系人不存在系统内时，打开终端配置自动更新，提示已同步通讯录 ([acb2fe4](https://git.kicad99.com/bfdx/bf8100-web/commit/acb2fe4ba23b50ae64929b245ad1a5f56f3e0ab8))
* poc终端通讯录不存在系统内不处理，不显示 ([69a827c](https://git.kicad99.com/bfdx/bf8100-web/commit/69a827c838eb66988b284fd9fa1943277d0e4b67))
* poc终端配置的终端上级单位必须在通讯录内，不可被移除；修复poc终端通讯录被移除后重新添加，直接出现在已收听组中 ([07324ef](https://git.kicad99.com/bfdx/bf8100-web/commit/07324ef82855cd0ccda43c719bc75661fbe19283))
* 传递给dataTable的表格滚动距离的计算多减去6px，（dataTable.vue中表格头部按钮栏添加6px的margin） ([ddc85b8](https://git.kicad99.com/bfdx/bf8100-web/commit/ddc85b83f53b12bd51f6ab604d3e82f73bde4458))
* 使用rid作用表格行的id,同步数据draw后，手动打开，保存表格滚动的行数 ([5a9615d](https://git.kicad99.com/bfdx/bf8100-web/commit/5a9615dabca6c43b68b21052125466432cadcb47))
* 修复存在多个表格时，切换语言，存在多个表格渲染在同一个页面的问题；在关闭/刷新网页时，清除保存的表格状态 ([29e4a47](https://git.kicad99.com/bfdx/bf8100-web/commit/29e4a476a220e8d67df4417432135f952fe47f66))
* 修复表格数据在切换语言destroy后init导致表格dom删除无法渲染的问题 ([3fb6076](https://git.kicad99.com/bfdx/bf8100-web/commit/3fb6076720cf09f172a143d0797ca2a940acc68d))
* 切换语言后，重新初始化表格，再次进入页面加载状态时，需要对状态的表格数据长度设置为数据长度 ([02ee4d9](https://git.kicad99.com/bfdx/bf8100-web/commit/02ee4d9aaa39f69781906fa2f0903cb1a9cf4c46))
* 删除log ([4cc98a3](https://git.kicad99.com/bfdx/bf8100-web/commit/4cc98a399fc71fcdded718bca212a82b2f2403d4))
* 可展开行单元格添加一个向左的padding ([1f69f1b](https://git.kicad99.com/bfdx/bf8100-web/commit/1f69f1bfc5752a579f06f753c45baa1ef16bf97f))
* 在draw后，需要将scrollTopRow存储，在页面激活时，恢复scrollTopRow，通过该state load一次表格 ([772b9f2](https://git.kicad99.com/bfdx/bf8100-web/commit/772b9f2a8e97a9f098bcef91cf4a3fc9ae56b8ca))
* 在同步数据后，重新draw表后，不能直接使用loadRestoreState回复状态，手动回复子行和滚动的距离 ([8897127](https://git.kicad99.com/bfdx/bf8100-web/commit/8897127257a242a9942b49ddd677525aac6fbd00))
* 在表格页面失活时保存状态，激活时恢复状态 ([464b8a5](https://git.kicad99.com/bfdx/bf8100-web/commit/464b8a52e0f42156e2df63a4456369a088b77d73))
* 存储过滤表格数据的字段,切换页面时恢复原来的数据 ([1995ff4](https://git.kicad99.com/bfdx/bf8100-web/commit/1995ff47eef0638900497d843095a0d1228cb3ef))
* 将dataTable相关package更新到最新版本，并修复dataTable.vue组件的样式处理 ([7977a67](https://git.kicad99.com/bfdx/bf8100-web/commit/7977a67bd540e0fbc95cc60b0f4bcb99b7521f7e))
* 数据更新需要重新draw一边的时候，需要手动调用展开行的show方法 ([23c6306](https://git.kicad99.com/bfdx/bf8100-web/commit/23c63065441f41399650038ca918fe68b8076ec4))
* 新建数据自动推荐一个未使用的dmrId， ([8f8d2bf](https://git.kicad99.com/bfdx/bf8100-web/commit/8f8d2bf4a2fcda626681de444db41c6349b31a7c))
* 更新表格数据和切换语言在draw的时候，添加上state（state）.draw ([0f1fb55](https://git.kicad99.com/bfdx/bf8100-web/commit/0f1fb550e70a0d18a314b16b80721b6178129f60))
* 添加一个缓存当前表格状态信息的对象，在同步表格数据后，重新的draw之后，恢复到之前的状态 ([f112412](https://git.kicad99.com/bfdx/bf8100-web/commit/f112412bb2ddfb7cca23cead3c42d652cd5836d9))
* 系统日志的第一条日志的内容都自动换行 ([81755ff](https://git.kicad99.com/bfdx/bf8100-web/commit/81755ffd4b41bbbe5d855eea3856b1a8b17cad33))
* 终端物联巡逻终端连续添加时dmrId自增，未满10位补0 ([920c73f](https://git.kicad99.com/bfdx/bf8100-web/commit/920c73fc49e942b49ad9048a3f4d79c26b7eead1))
* 终端类型切换为物联巡逻终端后清除一次规则校验， ([2727a12](https://git.kicad99.com/bfdx/bf8100-web/commit/2727a12c6a3b9c87f5e2b5ad9532b27cf7433603))
* 继续添加数据不用重置标签页数据，防止多次执行表单规则校验 ([6650687](https://git.kicad99.com/bfdx/bf8100-web/commit/665068774823135b64a46ff4717b15babce80814))
* 表格单击时已经改变了formData的dmrId，已经触发了dmrId的change校验，需要在单击数据时将weakMap存放的校验方法更新 ([f54f0b6](https://git.kicad99.com/bfdx/bf8100-web/commit/f54f0b6cd5cbc29efa897362b8badafaeb989d33))
* 表格实例绑定一个请求子行的方法，使用state 来draw时可以出发requestChild ([8365aa8](https://git.kicad99.com/bfdx/bf8100-web/commit/8365aa883c4f54065e941fb7e7712fd0af55e738))
* 设置minItemSize为44（2行），systemLogs添加频率过快，导致部分log渲染时高度还未计算出来，则使用的最小尺寸 ([392c6cb](https://git.kicad99.com/bfdx/bf8100-web/commit/392c6cb57f2be183cd74a233c374e71dd1ef3d8c))
* 设置minItemSize为44（2行），systemLogs添加频率过快，导致部分log渲染时高度还未计算出来，则使用的最小尺寸 ([e0e8260](https://git.kicad99.com/bfdx/bf8100-web/commit/e0e826095ab4a0e5c8c9ce1169d2625c5637b35b))
* 设置minItemSize为66（3行），systemLogs添加频率过快，导致部分log渲染时高度还未计算出来，则使用的最小尺寸 ([2ab48a0](https://git.kicad99.com/bfdx/bf8100-web/commit/2ab48a097707860b700ad137ee5df0f7d59d0a68))
* 调整td高度，防止导致出现不会出现滚动条的表格出现滚动条 ([1d6ecbe](https://git.kicad99.com/bfdx/bf8100-web/commit/1d6ecbea7fdf7d62322dc4c412d628761e5edd38))

## [3.2.2](https://git.kicad99.com/bfdx/bf8100-web/compare/v3.2.1...v3.2.2) (2024-10-18)


### Bug Fixes

* 检测定位时间是否过期 ([62e55b1](https://git.kicad99.com/bfdx/bf8100-web/commit/62e55b1bfe33c0ab3dcd0b2681fe36c304a49a56))

## [3.2.1](https://git.kicad99.com/bfdx/bf8100-web/compare/v3.2.0...v3.2.1) (2024-10-16)


### Bug Fixes

* dmrid校验时，先本地查找一次是否存在当前dmrid,没有则在服务器上查找一次是否存在 ([f68e64f](https://git.kicad99.com/bfdx/bf8100-web/commit/f68e64fcd6fb845009112623a09b61fbb7d373c9))
* dmrId校验时先判断辑状态 ([3b73734](https://git.kicad99.com/bfdx/bf8100-web/commit/3b737345e3a5159214de458f98bab4e570cf62e4))
* log清除 ([14ec257](https://git.kicad99.com/bfdx/bf8100-web/commit/14ec257605232e5e58eb286fa5d74611ea9f6931))
* 创建一个weakMap,在表单数据改变需要重新获取一遍规则时，使用formData作为key,validateDmrId做未value存储，在formData内存地址改变时，重新更新validateDmrId ([49c5f87](https://git.kicad99.com/bfdx/bf8100-web/commit/49c5f87892ada4c4e9a25fe6999e1e7dd5316909))
* 单位管理的表单的dmrid数据更改时才校验该属性 ([0c3450b](https://git.kicad99.com/bfdx/bf8100-web/commit/0c3450b37bfb90f5a6d4dd201358eff94b69b625))
* 只对dmrId进行唯一性规则处理 ([c35ef07](https://git.kicad99.com/bfdx/bf8100-web/commit/c35ef07f67af1f7486179d514d1acc119bb5cedb))
* 只对dmrId进行唯一性规则处理 ([c6dc159](https://git.kicad99.com/bfdx/bf8100-web/commit/c6dc1594f94f58ef2e18d431dfcd2fb199334a88))
* 地图标记点自编号唯一性校验 ([1a4683d](https://git.kicad99.com/bfdx/bf8100-web/commit/1a4683d1c31235b1d417ec4f2551dde601ffb708))
* 封装从服务器查找当前dmrid是否存在 ([1f0a529](https://git.kicad99.com/bfdx/bf8100-web/commit/1f0a52938482d25c3fbf26ed7e1c49bbc0ed3dcd))
* 封装校验dmrId是否唯一，传入相关参数返回校验的防抖函数 ([6868202](https://git.kicad99.com/bfdx/bf8100-web/commit/686820234d023794513aeecae95e0c8029be6b2c))
* 恢复误删除 ([1381f35](https://git.kicad99.com/bfdx/bf8100-web/commit/1381f35511a0f70c8d1c9b5c6d8a73a4919593ad))
* 撤回对显示“继续添加”文本的相关处理 ([d652422](https://git.kicad99.com/bfdx/bf8100-web/commit/d65242221cc6bf33205b5b8cd8fca4ed7dcbdda9))
* 数据添加表单确认添加成功后不自动关闭，而是表单部分数据自动加一，可点击确定继续添加 ([021195f](https://git.kicad99.com/bfdx/bf8100-web/commit/021195f57037ab3dd423c7467fffdff046b72326))
* 数据管理页面的添加表单添加按钮修改为“继续添加” ([9cd25f8](https://git.kicad99.com/bfdx/bf8100-web/commit/9cd25f8a8cf01e2a0a8d0a0db9d96d0fae5fc74c))
* 校验dmrId的函数重命名 ([bc25cf2](https://git.kicad99.com/bfdx/bf8100-web/commit/bc25cf21791c37cdf2ab85a2779c0b0f72b5959c))
* 添加一个防抖的检验dmrid的校验规则 ([c0585d3](https://git.kicad99.com/bfdx/bf8100-web/commit/c0585d31b6d95723592aa0b313fa99470e85beed))
* 添加数据完成标志注释 ([373c196](https://git.kicad99.com/bfdx/bf8100-web/commit/373c196b1e63a8024c7c8a37d02d53336e98963f))
* 添加数据完成标志注释 ([e9afc74](https://git.kicad99.com/bfdx/bf8100-web/commit/e9afc74b57744ba408ff64aa102ba0c056ec559f))
* 添加数据表单确定按钮在添加成功后变为自动添加按钮且部分数据+1处理 ([9bf39aa](https://git.kicad99.com/bfdx/bf8100-web/commit/9bf39aa53bb6ef8a243788f896bc5e5415a2342d))
* 添加终端数据成功标志 ([35e7061](https://git.kicad99.com/bfdx/bf8100-web/commit/35e7061aa115a0b90f1c11eeef0fef7a669af4e3))
* 用户页面表单的自编号添加唯一性校验 ([a88f90c](https://git.kicad99.com/bfdx/bf8100-web/commit/a88f90c90e1c04c332c4abe8dffab71d2e72a8f5))
* 终端dmrId在失去焦点时触发 ([2f00329](https://git.kicad99.com/bfdx/bf8100-web/commit/2f00329e53ca71e6d3c94b15dce113aca9157c51))
* 终端dmrid校验时，先本地查找一次是否存在当前dmrid,没有则在服务器上查找一次是否存在 ([324233f](https://git.kicad99.com/bfdx/bf8100-web/commit/324233fe9eef18cc31e182681c16a9764402bdf3))
* 终端dmrId添加唯一规则校验 ([fa2487f](https://git.kicad99.com/bfdx/bf8100-web/commit/fa2487f47686673b6c99a8efcc8408cf45b6a444))
* 终端selfId添加唯一规则校验 ([8658ebc](https://git.kicad99.com/bfdx/bf8100-web/commit/8658ebc0565741efc2cf5a2993fcb8b1919a6b27))
* 终端打开编辑数据框时，缓存当前终端的dmrId,用于在修改dmrId时校验dmrId是否唯一 ([4397209](https://git.kicad99.com/bfdx/bf8100-web/commit/439720911b5cf1bf732dbfa0591aac917d99b163))
* 群组dmrid添加唯一规则校验 ([4e3fea9](https://git.kicad99.com/bfdx/bf8100-web/commit/4e3fea978e5852aea559c0475d810e6543ab8461))
* 群组单位简称添加唯一规则校验 ([3668c0b](https://git.kicad99.com/bfdx/bf8100-web/commit/3668c0b1663498dc3b1da95de8c205addd149750))
* 群组自编号添加唯一规则校验 ([7ed954f](https://git.kicad99.com/bfdx/bf8100-web/commit/7ed954f9bd0c38d038d0f0c25e5835a4f031a0d1))
* 群组表单规则使用方法提供，每次修改表单内容都会自动校验一次 ([390d973](https://git.kicad99.com/bfdx/bf8100-web/commit/390d97343a1ada82b2764ccb25e807962a4c4ad7))
* 巡逻点表单页rfid添加唯一性校验 ([b462555](https://git.kicad99.com/bfdx/bf8100-web/commit/b462555c37137148781b016b8af6876c98307785))
* 通过dmrId清除服务器数据，失败后不需要提示 ([6b4c551](https://git.kicad99.com/bfdx/bf8100-web/commit/6b4c5511e470bcd899d322a494f99df7b13b133f))

## [3.2.0](https://git.kicad99.com/bfdx/bf8100-web/compare/v3.1.2...v3.2.0) (2024-10-08)


### Features

* enable mod-svt by default if not set ([f63d611](https://git.kicad99.com/bfdx/bf8100-web/commit/f63d61196938505fbaf7933d84b05f297eecf27c))
* hide svt controller if not enable svt ([4085650](https://git.kicad99.com/bfdx/bf8100-web/commit/4085650c44447524798440270da62ca48cf1ed78))

## [3.1.2](https://git.kicad99.com/bfdx/bf8100-web/compare/v3.1.1...v3.1.2) (2024-09-19)


### Bug Fixes

* poc终端子表联系人文本修改为通讯录 ([caab74c](https://git.kicad99.com/bfdx/bf8100-web/commit/caab74cf3b843f84ae4161df330f96765dbc2cf2))
* 在poc终端中直接点击确定更新poc终端后，代码错误导致更新了poc终端的密码 ([09e1db0](https://git.kicad99.com/bfdx/bf8100-web/commit/09e1db07498c3a453c82a817212b6347e2127f0e))

## [3.1.1](https://git.kicad99.com/bfdx/bf8100-web/compare/v3.1.0...v3.1.1) (2024-09-10)


### Bug Fixes

* poc device type fix 23 ([9e1b1cd](https://git.kicad99.com/bfdx/bf8100-web/commit/9e1b1cdf6675d745019918e882cc14f29faeb07c))
* poc终端子表联系人悬停显示tooltip展示所有 ([ff4b80a](https://git.kicad99.com/bfdx/bf8100-web/commit/ff4b80a5a6d749503140b7873cc72e146639426c))
* POC终端管理->POC终端配置 ([a4230fc](https://git.kicad99.com/bfdx/bf8100-web/commit/a4230fc8ece63051ac3c6d04bd68c159be1d7a12))
* poc终端管理页面的穿梭框里面的按钮取消buttonTexts ([d970f71](https://git.kicad99.com/bfdx/bf8100-web/commit/d970f719b75422ef29b96d62c7fa7cba7d94955a))
* 更新element-ui版本最低2.15.0到2.15.14，执行yarn install ([abfa0af](https://git.kicad99.com/bfdx/bf8100-web/commit/abfa0afd20df33c8ff8a1d85b723f48bd6cfa6b4))
* 穿梭框里面的按钮组使用flex col布局， 设置margin为0 ([787be6a](https://git.kicad99.com/bfdx/bf8100-web/commit/787be6a4ec5c3c6e2771eddce4da0dd09e8bccd3))
* 设备树添加边框 ([8f610ec](https://git.kicad99.com/bfdx/bf8100-web/commit/8f610ec87fc5915f848aad9aed290c5b1851fdf6))

## [3.1.0](https://git.kicad99.com/bfdx/bf8100-web/compare/v3.0.0...v3.1.0) (2024-08-26)


### Features

* 添加poc settings poc管理组件 ([2375383](https://git.kicad99.com/bfdx/bf8100-web/commit/23753833b328dee43c301ba9423a81831804b6f5))


### Bug Fixes

* PocSettingData在data中定义时clone一次default;节点取消勾选时，判断发射组是否取消； ([bb4e89d](https://git.kicad99.com/bfdx/bf8100-web/commit/bb4e89d2548c46b1dd295e9d2f500693e3081581))
* poc和网络对讲终端不需要设置信道数据 ([0cb7954](https://git.kicad99.com/bfdx/bf8100-web/commit/0cb7954af6a1dc98e0b343b2c80f5bec1af15279))
* poc管理添加一个穿梭框来选择接收组 ([ae1f8d5](https://git.kicad99.com/bfdx/bf8100-web/commit/ae1f8d5640c4d337584046dc2e5ff30c4a4a4231))
* poc终端子表展示poc终端的详细数据 ([faf894e](https://git.kicad99.com/bfdx/bf8100-web/commit/faf894e003dd21810fde184af77a833ca6de65eb))
* poc终端添加子表展示poc终端信息 ([397b5ff](https://git.kicad99.com/bfdx/bf8100-web/commit/397b5fff4e84eb9fd0c13e9fde015cbcc93c3454))
* poc终端的子表展示联系人，pc端最多展示2行，mobile最多展示4行 ([38494ff](https://git.kicad99.com/bfdx/bf8100-web/commit/38494ff7aa4552295e91d2fc8c7251706e760fb1))
* poc终端管理弹窗头部添加分割线 ([b0c16df](https://git.kicad99.com/bfdx/bf8100-web/commit/b0c16dffb89f4c94bbdea01584b20796e7ae5566))
* poc终端通讯录设置，双击默认发射组的节点时，判断非全部选中则全部选中否则全部取消且默认发射组也清空 ([3a469f4](https://git.kicad99.com/bfdx/bf8100-web/commit/3a469f45a028f370ed60d4548290c71c7bed5ccf))
* 以编辑模式进入的时候，会校验一次poc setting数据，并直接更新 ([4b09109](https://git.kicad99.com/bfdx/bf8100-web/commit/4b09109735174b3c3e3bc61dbf865e932836cf98))
* 优化poc终端管理组件数据与父组件数据同步时的问题 ([fc34fec](https://git.kicad99.com/bfdx/bf8100-web/commit/fc34fecda77818e7eb5e49e48498ff4d32b5cf56))
* 修复双击处理节点的所有子孙节点时，判断条件和获取children的对象出错导致的死循环 ([02e5fab](https://git.kicad99.com/bfdx/bf8100-web/commit/02e5fabd964e4e3b248299ef5ae8c54f41492a86))
* 删除未使用了的css ([9737645](https://git.kicad99.com/bfdx/bf8100-web/commit/97376456cc6dafde437e32e2d252ec65b99414f5))
* 删除未使用了的响应式变量 ([7509fad](https://git.kicad99.com/bfdx/bf8100-web/commit/7509fadcf2969735b84f35a60ff4720d1a335353))
* 双击事件执行完当前节点后，判断是否存在子节点，把子节点添加在数组最后，达到遍历该树的所有子孙节点 ([7bda507](https://git.kicad99.com/bfdx/bf8100-web/commit/7bda5074ab4d0f2450c2beb9db7327017eac3ffd))
* 双击时，已经拿到了当前节点的字节点，还需要拿到子节点下所有子节点 ([77b1bcd](https://git.kicad99.com/bfdx/bf8100-web/commit/77b1bcd0e5f01f4c2b67628267053f2121746e48))
* 双击默认发射组群组节点取消选中时，默认发射组同步被清除;清楚默认发射组时，不需要同步清楚通讯录的该节点 ([f7e2feb](https://git.kicad99.com/bfdx/bf8100-web/commit/f7e2feb7d15298ca587e31cd63323e1c7960ba7a))
* 变量名称优化 ([f1902bb](https://git.kicad99.com/bfdx/bf8100-web/commit/f1902bbb5058bc2ec11ed1f43f1493ff29797930))
* 在点击确定按钮后才将poc终端密码编码保存；更新终端信息时也需要判断终端是否为poc,判断发射组和密码是否存在，否则弹窗提示； ([15d4e72](https://git.kicad99.com/bfdx/bf8100-web/commit/15d4e729f1eab01fa20cfcfbf009b53e16788c5c))
* 在进入poc终端管理时，检测已被删除的终端和群组，存在即提示用户是否同步 ([3c58c58](https://git.kicad99.com/bfdx/bf8100-web/commit/3c58c581dcf15a229e82a2a4c520a2e446906d4a))
* 完成poc终端的添加 ([cc04b87](https://git.kicad99.com/bfdx/bf8100-web/commit/cc04b8754b08d6e88c3476c71174496edc93bc51))
* 将树中当前设备的节点禁用 ([d60b1e7](https://git.kicad99.com/bfdx/bf8100-web/commit/d60b1e7ca54b67e1301c9660124dba73c7b17547))
* 打开编辑框之前，把poc终端的pocSetting恢复为默认值，防止被其他poc终端的pocSetting污染 ([080d79d](https://git.kicad99.com/bfdx/bf8100-web/commit/080d79da8b2fe08a89a9ee20c928e42f05f78797))
* 新建poc终端进入poc终端管理时，将当前终端的所属群组当前做默认发射组和接收组 ([0f372c0](https://git.kicad99.com/bfdx/bf8100-web/commit/0f372c03b47fe26dd79bbafdb4dc3a3966e6ae23))
* 更新poc终端管理使用部份更新不使用全部更新更新 ([a28259e](https://git.kicad99.com/bfdx/bf8100-web/commit/a28259e3d3b3c51ddd0e5600fad54d632d5491a8))
* 更新协议，添加poc终端相关协议内容 ([59424cc](https://git.kicad99.com/bfdx/bf8100-web/commit/59424cccdc6da18a0ba730e65e90194bd049a8c8))
* 格式化语言json文件 ([16babd0](https://git.kicad99.com/bfdx/bf8100-web/commit/16babd07de00a3c945d85fd63c6f21be423103d1))
* 添加poc setting 的一些配置 ([1616dad](https://git.kicad99.com/bfdx/bf8100-web/commit/1616dad5b0e726efb8232c658edb0c4954fe95d7))
* 添加poc终端管理的通讯录等设置 ([e0a9327](https://git.kicad99.com/bfdx/bf8100-web/commit/e0a93276e99aaecd8cca97ca60a5a1487343eba7))
* 添加上未翻译的语音 ([82b61c3](https://git.kicad99.com/bfdx/bf8100-web/commit/82b61c32c7b7c877c8b5a0ba6faf4883f59edb86))
* 添加注释 ([aa12919](https://git.kicad99.com/bfdx/bf8100-web/commit/aa12919c36b58e080725adbf41deb50f1e8ba27c))
* 穿梭框在移动端时宽度100% ([8cf9f0f](https://git.kicad99.com/bfdx/bf8100-web/commit/8cf9f0ffcd29bd95cdf995e4312ae55d89a70139))
* 穿梭框高度调整，添加国际化翻译 ([db2e009](https://git.kicad99.com/bfdx/bf8100-web/commit/db2e009b7e94f51531ae4ed8a43336dd57cc5ff7))
* 终端管理中poc终端管理绑定的数据直接使用editRow的数据 ([4dcf46d](https://git.kicad99.com/bfdx/bf8100-web/commit/4dcf46df5bb1af0dc0606fe2d2adbe557a424b1d))
* 编辑poc终端管理数据，点击确定即可修改当前poc终端的pocSetting数据 ([1d3558b](https://git.kicad99.com/bfdx/bf8100-web/commit/1d3558b02ad4c99ee7a4614b1b9d86585692997a))
* 表格元素中点击子行自动选择子行的父元素 ([d26175e](https://git.kicad99.com/bfdx/bf8100-web/commit/d26175e17099bff67839b4b8160ed027ca5519ea))
* 适配移动端 ([fd39b7f](https://git.kicad99.com/bfdx/bf8100-web/commit/fd39b7f79577e391bfb943530722ac058d91893b))
* 适配移动端 ([17f8d0d](https://git.kicad99.com/bfdx/bf8100-web/commit/17f8d0dcafd3c892d50f77c844f87e17c7b45b03))
* 遍历全部节点更新联系人和接收组发射组数据时，判断默认发射组是否选中，未选中就使用当前设备的所属组 ([207459a](https://git.kicad99.com/bfdx/bf8100-web/commit/207459a952b7c8b674b3ff5911b614a5b0eebe6e))
* 重构双击节点时，全选或取消全选该节点下的全部子节点 ([b2eb509](https://git.kicad99.com/bfdx/bf8100-web/commit/b2eb509bb927a614fb5e03c19fe51ace631de9e7))
* 默认发射组默认添加到接收组；已收听组只剩一个时不可选择；接收组多个一起左移被清空时，将默认发射组添加到接收组 ([7a8b204](https://git.kicad99.com/bfdx/bf8100-web/commit/7a8b2040cdfa6c3a060a0f2957deea168ecc6c93))

## [3.0.0](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.60.0-pre.34...v3.0.0) (2024-08-20)


### Breaking

* 正式启用新版本UI，旧版本UI(master分支)归档处理 · 12 minutes ago

## [2.60.0-pre.34](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.60.0-pre.33...v2.60.0-pre.34) (2024-08-20)


### Bug Fixes

* getNewData方法执行时应返回一个新的defaultData对象 ([8f8d519](https://git.kicad99.com/bfdx/bf8100-web/commit/8f8d51927b5e05a326488c733802294f17966d4d))
* 使用回调函数判断是否继续添加 ([9c5b1c7](https://git.kicad99.com/bfdx/bf8100-web/commit/9c5b1c7a611312e5ed09f70bf9ed8b3059f2d7f0))
* 动态组添加判断“继续添加” ([6f2bca3](https://git.kicad99.com/bfdx/bf8100-web/commit/6f2bca3991aaea4476b1c0a448c222077e232aab))
* 单位管理添加“继续添加”功能 ([a83af80](https://git.kicad99.com/bfdx/bf8100-web/commit/a83af800ecd53b21623656e2c3517e7929d80cec))
* 数据表单通用组件的表单添加“继续添加”功能，终端管理添加“继续添加”功能 ([6f8fbe4](https://git.kicad99.com/bfdx/bf8100-web/commit/6f8fbe43feffdf4dbf7d8b8f8e047b6becfdda85))
* 数据表单通用组件的表单添加“继续添加”功能的判断不再beforeClose中执行，在父组件中的添加事件中判断, 地图标记点添加判断“继续添加” ([66c56ac](https://git.kicad99.com/bfdx/bf8100-web/commit/66c56ac4577c27735b091e90c8537688e7ba9efe))
* 注释 ([b7257df](https://git.kicad99.com/bfdx/bf8100-web/commit/b7257df7644d8c5e7db14a5ea9797a11ad6a0df2))
* 物联网终端添加判断“继续添加” ([1c7b46f](https://git.kicad99.com/bfdx/bf8100-web/commit/1c7b46f59079840cf4437f425e6f30c12e0fcc23))
* 用户管理添加“继续添加”功能 ([e357f51](https://git.kicad99.com/bfdx/bf8100-web/commit/e357f51e2bc692bcabdd22f5f9710b0a4a9980c8))
* 电话终端授权添加判断“继续添加” ([9a088a9](https://git.kicad99.com/bfdx/bf8100-web/commit/9a088a9dff3cb1c966a3d7e1212071e8bfdeef22))
* 电话网关黑白名单添加判断“继续添加” ([1d39a51](https://git.kicad99.com/bfdx/bf8100-web/commit/1d39a51b41effa525767096b8b5107fff03bc0a8))
* 电话网管短号映射添加判断“继续添加” ([81392df](https://git.kicad99.com/bfdx/bf8100-web/commit/81392df01e24bd7afe9351aae90a8a560a33c47c))
* 继续添加时通过defaultData创建一个新对象，对并新对象的部分属性进行加一处理后传入表单 ([14682b3](https://git.kicad99.com/bfdx/bf8100-web/commit/14682b305adbff0c4454fac176d182255cbed4f0))
* 职位管理添加“继续添加”功能 ([d2201ea](https://git.kicad99.com/bfdx/bf8100-web/commit/d2201ea255ccd3e85395f46e5c9e8a0fbcc579a2))
* 设备管理添加判断“继续添加” ([179a397](https://git.kicad99.com/bfdx/bf8100-web/commit/179a397bd7ef6e34a848c2cd1960a8c1505ab137))
* 巡逻点添加“继续添加”功能 ([bac46c3](https://git.kicad99.com/bfdx/bf8100-web/commit/bac46c3f78536ac1b96f9a01a03194361ed81d85))
* 巡逻线路添加“继续添加”功能 ([63a74d1](https://git.kicad99.com/bfdx/bf8100-web/commit/63a74d18b8fd3818f6f27d9ea9f1e0f774263000))
* 巡逻规则添加“继续添加”功能 ([b382f38](https://git.kicad99.com/bfdx/bf8100-web/commit/b382f38643ff347422c85766b8c543e4fda53312))
* 预定义电话薄添加判断“继续添加” ([f319bf6](https://git.kicad99.com/bfdx/bf8100-web/commit/f319bf6c0b5d11d6ac0224fa06a320eb2398ab7b))

## [2.60.0-pre.33](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.60.0-pre.32...v2.60.0-pre.33) (2024-08-19)


### Bug Fixes

* 修复CB08语音监控没有下发命令错误。页面打开时没有同步到调度台连接状态 ([c6f5f4f](https://git.kicad99.com/bfdx/bf8100-web/commit/c6f5f4fd386978133efccb83f5e6a57746342a3b))

## [2.60.0-pre.32](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.60.0-pre.31...v2.60.0-pre.32) (2024-08-16)


### Bug Fixes

* 写频通讯录组件的树节点的双击的节点深度选择方法重构-newui ([c0850db](https://git.kicad99.com/bfdx/bf8100-web/commit/c0850dbbf9c2421e36e39a46a51fef31a1216821))

## [2.60.0-pre.31](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.60.0-pre.30...v2.60.0-pre.31) (2024-08-16)


### Bug Fixes

* SVT信道不允许扫描；按键值部分命名错误修改为正确的，录音列表的会话id和时间格式转化 ([d72ff38](https://git.kicad99.com/bfdx/bf8100-web/commit/d72ff38b54dfdde5a71a098ffa51a9ba31500f65))
* 录音开始时间格式化 ([72c3e3e](https://git.kicad99.com/bfdx/bf8100-web/commit/72c3e3ecb30a906de16c7167eb6960d75fdb10e1))
* 录音开始时间格式化 ([6c18f88](https://git.kicad99.com/bfdx/bf8100-web/commit/6c18f8878aab7d0e9b4650f7087a9af34146c048))
* 测试代码删除 ([d7c06a6](https://git.kicad99.com/bfdx/bf8100-web/commit/d7c06a6caee6213d0270db48099a186b342c18df))

## [2.60.0-pre.30](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.60.0-pre.29...v2.60.0-pre.30) (2024-08-15)


### Bug Fixes

* 修复控制器关联的终端缺失"pocSettingLastModifyTime"导致添加失败问题 [release] ([a9cba46](https://git.kicad99.com/bfdx/bf8100-web/commit/a9cba464585164720804c010a770624c75e512d0))

## [2.60.0-pre.29](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.60.0-pre.28...v2.60.0-pre.29) (2024-08-14)


### Bug Fixes

* 修改树节点默认排序方法，单位节点必须按排序值排序 [release] ([dac763d](https://git.kicad99.com/bfdx/bf8100-web/commit/dac763d7164c0592fab053aea31ca2728eb12676))

## [2.60.0-pre.28](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.60.0-pre.27...v2.60.0-pre.28) (2024-08-09)


### Bug Fixes

* newui add device need add pocSettingLastModifyTime ([820aa4e](https://git.kicad99.com/bfdx/bf8100-web/commit/820aa4effba0873308d88cd061668893317ddfd5))

## [2.60.0-pre.27](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.60.0-pre.26...v2.60.0-pre.27) (2024-08-08)


### Bug Fixes

* fix db poc session cmd error ([eef0897](https://git.kicad99.com/bfdx/bf8100-web/commit/eef0897920f6c3bab9f9bbdf15ae8933462db8c3))
* minServerVersion: 2.92.14 [release] ([93d8146](https://git.kicad99.com/bfdx/bf8100-web/commit/93d814654a1d42e2afae6562cdfdc68129208498))

## [2.60.0-pre.26](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.60.0-pre.25...v2.60.0-pre.26) (2024-08-07)


### Bug Fixes

* en delete duplicate key and add not exist key value ([6270959](https://git.kicad99.com/bfdx/bf8100-web/commit/6270959490b4fa02c15b0eb4e6d1032b8caa953f))
* fr delete duplicate key and add not exist key value ([d6d5eab](https://git.kicad99.com/bfdx/bf8100-web/commit/d6d5eab3661f3db8d89fef55dee410163ee9513e))
* zh-cn delete duplicate key ([331211c](https://git.kicad99.com/bfdx/bf8100-web/commit/331211c122d5cd87ae89e99655e26b5126a2ba56))
* 点击五次后，系统日志展示，和运行日志分为两栏 ([69205fd](https://git.kicad99.com/bfdx/bf8100-web/commit/69205fd68c07a9471ea5364c5bd51296b06af00a))
* 系统日志 el-card__body padding set 0 ([91ab0c5](https://git.kicad99.com/bfdx/bf8100-web/commit/91ab0c5e85fc34aa6e227e11b40f442330791b32))
* 顶部导航栏tag关闭时，发布remove-tag事件，运行日志订阅remove-tag事件，判断为notes时注销系统日志组件 ([1a98a68](https://git.kicad99.com/bfdx/bf8100-web/commit/1a98a6810aa465dd394133cda781b679de9c1aa0))

## [2.60.0-pre.25](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.60.0-pre.24...v2.60.0-pre.25) (2024-08-07)


### Bug Fixes

* minServerVersion 2.92.13 ([3bd70e7](https://git.kicad99.com/bfdx/bf8100-web/commit/3bd70e7582b79d7297751d952b27ffb1af111398))

## [2.60.0-pre.24](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.60.0-pre.23...v2.60.0-pre.24) (2024-08-07)


### Bug Fixes

* 更新协议 ([5173450](https://git.kicad99.com/bfdx/bf8100-web/commit/5173450323ef7d67f13c546a3bd7d5f7ed9c4c53))
* 巡逻历史接收时间utc to local ([d572912](https://git.kicad99.com/bfdx/bf8100-web/commit/d572912c54193ef85ea4935876950ea895f5be98))
* 巡逻历史添加接收时间 ([55c6c7f](https://git.kicad99.com/bfdx/bf8100-web/commit/55c6c7f454645d02748821e1a9ff21e1c8aff1ab))

## [2.60.0-pre.23](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.60.0-pre.22...v2.60.0-pre.23) (2024-08-01)


### Features

* 添加860sdc svt写频页面 ([0836cdf](https://git.kicad99.com/bfdx/bf8100-web/commit/0836cdf47fa79e7dbb4777647ad9c2c06bbb1425))


### Bug Fixes

* 860sdc完善读取录音文件数据功能 ([46fc717](https://git.kicad99.com/bfdx/bf8100-web/commit/46fc717b4efcfb3ce2a76ebcf6629fe8b80ee891))
* 860写频的设备信息中添加选配功能展示，读取玩数据后在发送指令获取ui版本信息 ([7bd110c](https://git.kicad99.com/bfdx/bf8100-web/commit/7bd110c6cb465318e52b63f05c213468842d4f40))
* data-set传递的属性名会转化为全小写 ([6d1a6cc](https://git.kicad99.com/bfdx/bf8100-web/commit/6d1a6cc9f2ce629cd6a5e5e841268f6db3d60cf6))
* SDC860协议添加新建组呼联系人和回复信道 ([a59b78b](https://git.kicad99.com/bfdx/bf8100-web/commit/a59b78bb76d8d819868265983a554e3338a10e49))
* SVT860协议添加新建组呼联系人和回复信道 ([34cfe25](https://git.kicad99.com/bfdx/bf8100-web/commit/34cfe25bd526b4a727a90a60bff166bac8f29a85))
* 删除模拟报警除报警类型以外的配置选项 ([d41ac1d](https://git.kicad99.com/bfdx/bf8100-web/commit/d41ac1d442e5df7e96b32ac35d7b1a8536c323b0))
* 同步860SDC和SVT的写频改动到新ui上 ([80f5e5a](https://git.kicad99.com/bfdx/bf8100-web/commit/80f5e5ad45ff0ec34bb9d440575c1fa817b18770))
* 同步860sdc在旧ui上面的改动 ([e0d0b9d](https://git.kicad99.com/bfdx/bf8100-web/commit/e0d0b9dbd45696ee178e6914f0235adc89e15d7c))
* 同步国际化翻译 ([16a2c36](https://git.kicad99.com/bfdx/bf8100-web/commit/16a2c36c45a513ad0948248f399acec9f503a16f))
* 同步添加860SVT写频到新ui ([163f602](https://git.kicad99.com/bfdx/bf8100-web/commit/163f60290b7db12240dd6b945136b842a6a0c5d8))
* 录音列表表格选中列添加全选checkbox ([0f7227e](https://git.kicad99.com/bfdx/bf8100-web/commit/0f7227e254b660ddfec39e26f7dfb05fe9d71a6a))
* 恢复对讲机写频打开的默认机型 ([dce0329](https://git.kicad99.com/bfdx/bf8100-web/commit/dce0329b2af32c55ef537722240760bee58723b6))
* 添加860 型号的modelInfo ([5da555c](https://git.kicad99.com/bfdx/bf8100-web/commit/5da555c62c300a589d7b09ed579f72ad1c098dc7))
* 添加BP860SDC页面 ([872e06c](https://git.kicad99.com/bfdx/bf8100-web/commit/872e06c097c288c6de6ed166b319fbfd53c66acb))
* 添加协议文档和国际化翻译 ([79c82da](https://git.kicad99.com/bfdx/bf8100-web/commit/79c82da74c2a2fac3e891baab912dc8eebcedfac))
* 添加获取录音组件 ([a178690](https://git.kicad99.com/bfdx/bf8100-web/commit/a178690d6d9a5a964cf5f0e0fa3db167d60cb22c))
* 适配功能动态添加树节点 ([ab9de03](https://git.kicad99.com/bfdx/bf8100-web/commit/ab9de0361b9e073f0c8b330a9a8f351d8b5faac4))

## [2.60.0-pre.22](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.60.0-pre.21...v2.60.0-pre.22) (2024-07-30)


### Bug Fixes

* 修改服务器小版本为v2.92.10 ([06adaab](https://git.kicad99.com/bfdx/bf8100-web/commit/06adaab4fb00e317ca15c7c40896132b4397c660))
* 网页登录后需要验证后台版本，不合适的话，提示 ([66f78d0](https://git.kicad99.com/bfdx/bf8100-web/commit/66f78d0ed96a152a0f199bd61ae869f4846b72d2))

## [2.60.0-pre.21](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.60.0-pre.20...v2.60.0-pre.21) (2024-07-29)


### Bug Fixes

* update and rebuild proto ([cfcf899](https://git.kicad99.com/bfdx/bf8100-web/commit/cfcf89908041b43e27dc73822dea7014974da952))
* 修改登录请求的应答消息展示，显示没有没有登录调度管理权限 ([6cb7476](https://git.kicad99.com/bfdx/bf8100-web/commit/6cb747653c28a5e0ba751c595a0bf436174a8736))
* 用户权限添加允许登录调度管理网页选项 ([c584845](https://git.kicad99.com/bfdx/bf8100-web/commit/c58484591eb457293c958189cd1c8a366e6aa4c1))

## [2.60.0-pre.20](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.60.0-pre.19...v2.60.0-pre.20) (2024-07-29)


### Bug Fixes

* 使用Promise.allSettled判断status是否成功删除，最后只进行一次filter ([138f35e](https://git.kicad99.com/bfdx/bf8100-web/commit/138f35e6da71f87e297ebba31bbc918fcf986214))
* 使用Promise.allSettled判断status是否成功删除，最后只进行一次filter ([4f11eec](https://git.kicad99.com/bfdx/bf8100-web/commit/4f11eec6b9e8918380f032dbe9be7b1def7719ba))
* 修复取消单个位置授权按钮的tooltip距离过高问题 ([1fc1ddb](https://git.kicad99.com/bfdx/bf8100-web/commit/1fc1ddb885a1be27760a1ba23f259ab1b8249df5))
* 终端更新-终端位置授权显示“已申请”和“以授权”的记录, 取消权限时需要检测是否存在编辑用户数据权限 ([6cf2429](https://git.kicad99.com/bfdx/bf8100-web/commit/6cf2429cbd3aba6ae88a4fa7dacbdb1eb07bf212))

## [2.60.0-pre.19](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.60.0-pre.18...v2.60.0-pre.19) (2024-07-26)


### Bug Fixes

* 修改切换语言菜单以二级菜单方式动态加载，添加新的语言翻译，需要配置config.js的languages项 ([88213f9](https://git.kicad99.com/bfdx/bf8100-web/commit/88213f907828340d3dd44304d7acd0f184a2ad10))
* 添加语言翻译动态配置说明 ([e80680c](https://git.kicad99.com/bfdx/bf8100-web/commit/e80680c0f5e88a13a330a4e7b00599ad9d25e653))

## [2.60.0-pre.18](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.60.0-pre.17...v2.60.0-pre.18) (2024-07-26)


### Features

* 对app请求地图显示其它终端的需求作出回应-new-ui" ([1dca383](https://git.kicad99.com/bfdx/bf8100-web/commit/1dca38399b95086aad7471c6c10f651d87b3643e))


### Bug Fixes

* 同时取消多条时，只展示一次取消成功 ([91f2645](https://git.kicad99.com/bfdx/bf8100-web/commit/91f26453f6748bcd72b73b871ea45fa4a96acf4b))
* 同时取消多条时，只展示一次取消成功 ([240ea27](https://git.kicad99.com/bfdx/bf8100-web/commit/240ea2781cf8621beb08302b8108d8ff869055f9))

## [2.60.0-pre.17](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.60.0-pre.16...v2.60.0-pre.17) (2024-07-22)


### Bug Fixes

* 添加虚拟集群(SVT)归属组的vue实例混合配置，将写频功能SVT归属组"无"改为"基于信道默认组呼" [release] ([c1814b7](https://git.kicad99.com/bfdx/bf8100-web/commit/c1814b7c87f359de81b91d6cbfeb821fdb53631f))

## [2.60.0-pre.16](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.60.0-pre.15...v2.60.0-pre.16) (2024-07-05)


### Bug Fixes

* 修复同频同播中继和虚拟集群中继的写频功能判断是否注册异常 ([f180503](https://git.kicad99.com/bfdx/bf8100-web/commit/f1805035d2e2c217ed71ccb01d82192f9c663b33))
* 修复同频同播中继和虚拟集群中继的写频功能接收应答的订阅主题被提前解除问题 ([1825e02](https://git.kicad99.com/bfdx/bf8100-web/commit/1825e021471cc08fb02593b2d0477b5f2a40b81d))

## [2.60.0-pre.15](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.60.0-pre.14...v2.60.0-pre.15) (2024-05-15)


### Bug Fixes

* map popup close button has no  padding ([076b06f](https://git.kicad99.com/bfdx/bf8100-web/commit/076b06f82658c6418d9f9d91f36ed0ea72682f39))

## [2.60.0-pre.14](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.60.0-pre.13...v2.60.0-pre.14) (2024-04-17)


### Bug Fixes

* 修改"MESH终端"为"MC-N终端" ([7da8044](https://git.kicad99.com/bfdx/bf8100-web/commit/7da804409c27bfe3baf0d4138a0e00fcf8970d5c))
* 修改"MESH网关"为"MC-N网关" ([1f7fc2d](https://git.kicad99.com/bfdx/bf8100-web/commit/1f7fc2d5798e8e3a920fdc99f9c0753c68375337))

## [2.60.0-pre.13](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.60.0-pre.12...v2.60.0-pre.13) (2024-04-12)


### Bug Fixes

* 修复copyFieldsFromProto方法拷贝数据源自定义方法判断错误 ([a3cfaea](https://git.kicad99.com/bfdx/bf8100-web/commit/a3cfaea4788c24168cda573cf9469d27d9d7e510))

## [2.60.0-pre.12](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.60.0-pre.11...v2.60.0-pre.12) (2024-04-08)


### Bug Fixes

* 按工程部反馈的定制需要，将“巡查”改为“巡逻” ([71b6fb7](https://git.kicad99.com/bfdx/bf8100-web/commit/71b6fb74479add0780c22e8ce767cb5cf377950a))

## [2.60.0-pre.11](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.60.0-pre.10...v2.60.0-pre.11) (2024-04-07)


### Bug Fixes

* 优化拷贝proto消息结构方法，把数据源的自定义的属性也拷贝一份 ([0cd57c9](https://git.kicad99.com/bfdx/bf8100-web/commit/0cd57c98398f47cd9183f4770e152ea618b9da00))

## [2.60.0-pre.10](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.60.0-pre.9...v2.60.0-pre.10) (2024-04-03)


### Bug Fixes

* update ci ([1f2c86f](https://git.kicad99.com/bfdx/bf8100-web/commit/1f2c86f3ad72badc3fc1d3cba1484dd7d9cd641a))

## [2.60.0-pre.9](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.60.0-pre.8...v2.60.0-pre.9) (2024-04-01)


### Bug Fixes

* 修复新版本UI更新信道配置后，没有同步父级组件数据，导致终端更新时使用了旧的信道数据的异常 ([419e296](https://git.kicad99.com/bfdx/bf8100-web/commit/419e2967e4fee4d9e3a3d099ba2f644bd6a760f7))
* 修正新版UI终端信道管理的默认发射组没有自动设置问题，保持与旧版行为一致 ([0dba373](https://git.kicad99.com/bfdx/bf8100-web/commit/0dba3731c65212196811b8ba1a4b7dd8430f922f))
* 删除不需要的channels定义 ([f3bc65c](https://git.kicad99.com/bfdx/bf8100-web/commit/f3bc65c6920bff8194ad0094f36847a605cf46c7))

## [2.60.0-pre.8](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.60.0-pre.7...v2.60.0-pre.8) (2024-03-18)


### Bug Fixes

* 修改设备管理的"电话网关"名称为"TG810网关" ([4b98caa](https://git.kicad99.com/bfdx/bf8100-web/commit/4b98caac7730c916278fd5fc6161ed794b552a16))

## [2.60.0-pre.7](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.60.0-pre.6...v2.60.0-pre.7) (2024-02-28)


### Bug Fixes

* 删除在菜单配置多添加的对讲机配置选项组 ([dee26dc](https://git.kicad99.com/bfdx/bf8100-web/commit/dee26dc23947a4d4b641639cd1ea9e38c9a431f9))
* 删除模板注释代码，修改按键定义中v-for的key绑定值；读取数据前清空所有数据 ([7a8cd74](https://git.kicad99.com/bfdx/bf8100-web/commit/7a8cd74df04aeb7ffce2b3a9a4b57e400663a59a))
* 同步master分支上td920和bp660更改到新ui ([08cca16](https://git.kicad99.com/bfdx/bf8100-web/commit/08cca16a41a06e04c0803596d3d5643b050e23ac))
* 屏蔽扫描-配置项；添加国际化翻译功率切换_bp660；设定扫描穿梭框最大高度 ([75a5735](https://git.kicad99.com/bfdx/bf8100-web/commit/75a57353834c9cde377ab73221ac9c4b20170313))
* 开始读取数据前情况选择写入的设备，和所有设备数据 ([4d0b45f](https://git.kicad99.com/bfdx/bf8100-web/commit/4d0b45fa8ec347882d1f3c8bf8bf00e042ab5e1a))
* 设定扫描、漫游穿梭框最大高度 ([c75b366](https://git.kicad99.com/bfdx/bf8100-web/commit/c75b36667ce059862d31d5002db55cd7d06a9bf5))

## [2.60.0-pre.6](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.60.0-pre.5...v2.60.0-pre.6) (2024-02-26)


### Bug Fixes

* 修复在打开编辑界面时，表格重绘导致编辑页面当前数据变成另一个打开子表格行的行数据 ([ddc9d4d](https://git.kicad99.com/bfdx/bf8100-web/commit/ddc9d4dfdb2a6d78c5dec3034563f36c0cd4e979))
* 修复表格重绘导致添加和编辑页面的数据被表格行数据覆盖 ([a80da37](https://git.kicad99.com/bfdx/bf8100-web/commit/a80da37ddd1418b3ab3fb1a7a307ff9452ad073b))

## [2.60.0-pre.5](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.60.0-pre.4...v2.60.0-pre.5) (2024-02-02)


### Bug Fixes

* 修复new-ui终端管理在信道更新时因note字段不存在导致的表格渲染警告提示 ([c2c0bfb](https://git.kicad99.com/bfdx/bf8100-web/commit/c2c0bfbfc9c604eea94eb04dcbca80af6100b00e))
* 解决数据更新时，全局数据中被更新的数据的原型被更改导致dataTable报'缺少字段note'的错 ([c06852f](https://git.kicad99.com/bfdx/bf8100-web/commit/c06852f1cca00ae45856f7809006d1daafbca746))

## [2.60.0-pre.4](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.60.0-pre.3...v2.60.0-pre.4) (2024-02-02)


### Bug Fixes

* fix package.json error, missing some modules ([48a96ab](https://git.kicad99.com/bfdx/bf8100-web/commit/48a96ab1e66e36adf2af4238bc1edd97a6f40639))

## [2.60.0-pre.3](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.60.0-pre.2...v2.60.0-pre.3) (2024-02-02)


### Bug Fixes

* fix package.json version v2.59.3 ([2a61b2c](https://git.kicad99.com/bfdx/bf8100-web/commit/2a61b2cc15a457a68c89439af467c141861f077a))
* 修复下发的指令超时检测逻辑，缓存指令序号进行判断 ([c49883b](https://git.kicad99.com/bfdx/bf8100-web/commit/c49883ba3677f110fd47da8dafc02c6da63228db))

## [2.59.4](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.59.3...v2.59.4) (2024-02-02)


### Bug Fixes

* fix package.json version v2.59.3 ([2a61b2c](https://git.kicad99.com/bfdx/bf8100-web/commit/2a61b2cc15a457a68c89439af467c141861f077a))

## [2.59.2](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.59.1...v2.59.2) (2024-02-02)


### Bug Fixes

* delete unused file ([1b5e6f7](https://git.kicad99.com/bfdx/bf8100-web/commit/1b5e6f7ec709dca6a1ba07f74d9d9def2496da30))
* edit ci config ([d7deb58](https://git.kicad99.com/bfdx/bf8100-web/commit/d7deb582f7e7757393e200b0b3f8a48124589b9b))
* 修复下发的指令超时检测逻辑，缓存指令序号进行判断 ([c49883b](https://git.kicad99.com/bfdx/bf8100-web/commit/c49883ba3677f110fd47da8dafc02c6da63228db))
* 修复区域查找时，bc42指令没有删除相关的超时标记导致继续提示没有回应的异常 ([76f12a2](https://git.kicad99.com/bfdx/bf8100-web/commit/76f12a28b939eef4b96617041e6abb6f41bf69fa))


## [2.59.2](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.59.1...v2.59.2) (2024-02-02)


### Bug Fixes

* delete unused file ([1b5e6f7](https://git.kicad99.com/bfdx/bf8100-web/commit/1b5e6f7ec709dca6a1ba07f74d9d9def2496da30))
* edit ci config ([d7deb58](https://git.kicad99.com/bfdx/bf8100-web/commit/d7deb582f7e7757393e200b0b3f8a48124589b9b))
* 修复区域查找时，bc42指令没有删除相关的超时标记导致继续提示没有回应的异常 ([76f12a2](https://git.kicad99.com/bfdx/bf8100-web/commit/76f12a28b939eef4b96617041e6abb6f41bf69fa))

## [2.60.0-pre.1](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.59.2...v2.60.0-pre.1) (2024-02-01)


### Features

* 在发送命令窗口添加卫星定位开关操作命令 ([7fbb565](https://git.kicad99.com/bfdx/bf8100-web/commit/7fbb565a2f190063f6f16f4b715ba94ed3fbfe43))
* 更新协议 ([1c85bce](https://git.kicad99.com/bfdx/bf8100-web/commit/1c85bce192d01691df2c5e54f0329b95d188386f))


### Bug Fixes

* 修改bc42指令处理逻辑，添加其他GPS相关指令检测并进行消息提示 ([8c420c3](https://git.kicad99.com/bfdx/bf8100-web/commit/8c420c3fdf3d8c33abf422123d4e293cccf37567))
* 修改卫星定位开关超时应答消息 ([d990567](https://git.kicad99.com/bfdx/bf8100-web/commit/d9905671b97a47cdd2f581ea2691143cefbd8cb0))
* 修正卫星定位开关命令布局 ([3e3f480](https://git.kicad99.com/bfdx/bf8100-web/commit/3e3f480c7c41fd83615e6a5eed4fe2286da1d75d))
* 合并master分支，解决语言包冲突 ([6d8e6f3](https://git.kicad99.com/bfdx/bf8100-web/commit/6d8e6f34889ac0f39bdde5892ccd800c4a07e7cb))

## [2.59.2](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.59.1...v2.59.2) (2024-02-02)


### Bug Fixes

* edit ci config ([d7deb58](https://git.kicad99.com/bfdx/bf8100-web/commit/d7deb582f7e7757393e200b0b3f8a48124589b9b))
* 修复区域查找时，bc42指令没有删除相关的超时标记导致继续提示没有回应的异常 ([76f12a2](https://git.kicad99.com/bfdx/bf8100-web/commit/76f12a28b939eef4b96617041e6abb6f41bf69fa))

## [2.59.1](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.59.0...v2.59.1) (2024-02-01)


### Bug Fixes

* 修改bc42指令处理逻辑，添加其他GPS相关指令检测并进行消息提示 ([8c420c3](https://git.kicad99.com/bfdx/bf8100-web/commit/8c420c3fdf3d8c33abf422123d4e293cccf37567))

## [2.59.0](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.58.1...v2.59.0) (2024-01-31)


### Features

* 在发送命令窗口添加卫星定位开关操作命令 ([7fbb565](https://git.kicad99.com/bfdx/bf8100-web/commit/7fbb565a2f190063f6f16f4b715ba94ed3fbfe43))
* 更新协议 ([1c85bce](https://git.kicad99.com/bfdx/bf8100-web/commit/1c85bce192d01691df2c5e54f0329b95d188386f))


### Bug Fixes

* 修改卫星定位开关超时应答消息 ([d990567](https://git.kicad99.com/bfdx/bf8100-web/commit/d9905671b97a47cdd2f581ea2691143cefbd8cb0))

## [2.58.1](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.58.0...v2.58.1) (2024-01-29)


### Bug Fixes

* BP660写频界面的菜单设置同步写频软件的界面配置 ([dcdc781](https://git.kicad99.com/bfdx/bf8100-web/commit/dcdc781f3322617c055ae8c263dcad48d2a64993))
* 与写频软件各个界面选项确定，修复协议的错误 ([ac3f339](https://git.kicad99.com/bfdx/bf8100-web/commit/ac3f339b3b2b4bc7054856bc78677a35f9c60b26))
* 信令系统添加选项 其他配置添加1-2个选项 ([7e3dcc6](https://git.kicad99.com/bfdx/bf8100-web/commit/7e3dcc6bdf2398e459a2be397a152f1ec5d34f7d))
* 协议添加录音文件呼入呼出，在写入之前选择要添加的设备后，清空按键定义中非本系统的数据 ([0aa7bc9](https://git.kicad99.com/bfdx/bf8100-web/commit/0aa7bc9dfef5ce08a9078feeba906e8ce0722e8c))
* 取消对终端管理的prochatDeviceSelect字段的prop字段添加，在经纬度表单项添加对于校验规则的prop字段名称 ([5377184](https://git.kicad99.com/bfdx/bf8100-web/commit/5377184fb595606a864a0b87448e162a009d8519))
* 将信道管理字段的非确认单呼短信取反更名为已确认数据单呼 ([db0831c](https://git.kicad99.com/bfdx/bf8100-web/commit/db0831cfab4fd7fc46902f1ead61509df6915c59))
* 常规设置的定位模式添加格洛纳斯和伽利略 ([7b061cc](https://git.kicad99.com/bfdx/bf8100-web/commit/7b061ccec8b5ae6da8f4182a95da9497c19edc40))
* 格式化代码 ([ec95b70](https://git.kicad99.com/bfdx/bf8100-web/commit/ec95b701313e16336b4c847269bc1fe1d5381261))
* 格式化代码 ([c126d34](https://git.kicad99.com/bfdx/bf8100-web/commit/c126d34bb8f1a01023bea1de5d4b9803486f7dec))
* 格式化代码 ([933ddd4](https://git.kicad99.com/bfdx/bf8100-web/commit/933ddd408f87713ea3527c3a365d3b9856518fff))
* 清除propchat终端列表组件prop的添加，使用nextTick包裹重置表单后的代码逻辑，使得重置表单方法先于数据覆盖之前执行 ([9d254f2](https://git.kicad99.com/bfdx/bf8100-web/commit/9d254f2a5ebdcea17ad1fd08c6cc74e86f403874))
* 漫游项ui调整，短信同步时触发监听器，对高频触发的监听器进行防抖处理,协议虚拟集群字段步进值更改 ([329dfe9](https://git.kicad99.com/bfdx/bf8100-web/commit/329dfe907d98907f3a6a4def7dade95de54545a1))
* 终端管理的更新表单中的表单项添加prop字段，在存在校验和清除表单的情况下是必填的 ([2dd02fd](https://git.kicad99.com/bfdx/bf8100-web/commit/2dd02fd9a1f1f52c5cf5e87b553b48683223fc49))

## [2.58.0-pre.2](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.58.0-pre.1...v2.58.0-pre.2) (2024-01-25)


### Bug Fixes

* gitlab ci did not cache package.json in build-new-ui job ([41abf41](https://git.kicad99.com/bfdx/bf8100-web/commit/41abf4110d0bbe39b22b792c24ce7694c3bbbd84))

## [2.58.0-pre.1](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.57.1...v2.58.0-pre.1) (2024-01-25)


### Features

* add detailRender for prochat device ([62daf3e](https://git.kicad99.com/bfdx/bf8100-web/commit/62daf3eb1e38eab1360de6ec9aea4dc56a85452d))
* use requestIdleCallback func to resolve gprochatDeviceInfoList ([4298b5f](https://git.kicad99.com/bfdx/bf8100-web/commit/4298b5f78bbef1707a7efaac8e434e9d86aa6eb0))
* 如果prochat终端更改类型为其他，删除msID与prochat终端的映射 ([4d6070d](https://git.kicad99.com/bfdx/bf8100-web/commit/4d6070d5e713fbaa77d1f8f593942aa324442066))


### Bug Fixes

* config semantic-release with wrong argument ([915fe43](https://git.kicad99.com/bfdx/bf8100-web/commit/915fe43a981dbe125a9daffbf05244c0c1c77600))
* dataTable添加自定义表格的dom字段属性 ([39e5cb4](https://git.kicad99.com/bfdx/bf8100-web/commit/39e5cb415a2ef1f6022762ca6b11ba2230e45a86))
* 信道管理收听组切换的按钮组的样式调整 ([a3261b2](https://git.kicad99.com/bfdx/bf8100-web/commit/a3261b2166ba97462337786e5fde388da7b6a0ac))
* 信道管理收听组切换的按钮组的样式调整 ([7510644](https://git.kicad99.com/bfdx/bf8100-web/commit/75106445aae71469edda1fc09f9563f46f8be94c))
* 信道管理收听组切换的按钮组的样式调整 ([8dfa737](https://git.kicad99.com/bfdx/bf8100-web/commit/8dfa7371589492fba6ce1e97a804cc12a5825e5b))
* 修复新版本UI在先打开联网通话再打开发送命令窗口时，无法下发语音监控问题 ([a1549f1](https://git.kicad99.com/bfdx/bf8100-web/commit/a1549f1f63baa1910606b3e6c8e121f9011de4e4))
* 对读取录音文件信息时读取为空进行重复读取10次 ([4b3ee6f](https://git.kicad99.com/bfdx/bf8100-web/commit/4b3ee6f926fd0e23ea58af7988290ae4e798cc8e))
* 将等待函数完成的函数改为睡眠函数添加到bfutil ([ce504d8](https://git.kicad99.com/bfdx/bf8100-web/commit/ce504d87eee2902acbd15f1369bc6589d0bd8148))
* 无法修改除prochat网关终端类型之外的终端数据 ([c64a649](https://git.kicad99.com/bfdx/bf8100-web/commit/c64a649f10eca29f38ec2beca074cb425294622c))
* 禁止由其他设备类型更改为Prochat网关类型 ([f23f4cc](https://git.kicad99.com/bfdx/bf8100-web/commit/f23f4cc5a6d1f9d4826e342225cec91507106fe3))


## [2.58.0](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.57.0...v2.58.0) (2024-01-23)


### Features

* 增加 isUserOperationProcessing，表示当前用户的增加、更新和删除操作是否完成 ([720067e](https://git.kicad99.com/bfdx/bf8100-web/commit/720067efda979b5af2574ac84423c2f253934660))
* 如果prochat终端更改类型为其他，删除msID与prochat终端的映射 ([e8667b8](https://git.kicad99.com/bfdx/bf8100-web/commit/e8667b89ca39d53d1d808a4fd3ce0a6a004642ec))


### Bug Fixes

* 使用了错误的addData导致无法增加prochat终端 ([a6e6a45](https://git.kicad99.com/bfdx/bf8100-web/commit/a6e6a458fbab9274f816fd1ea0345b7a36be5c9b))

## [2.57.0](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.56.3...v2.57.0) (2024-01-17)


### Features

* use requestIdleCallback func to resolve gprochatDeviceInfoList ([7195abe](https://git.kicad99.com/bfdx/bf8100-web/commit/7195abe1c3973eeccf35a0f7883fe444afd8c858))


### Bug Fixes

* can not update device upData ([de16bfa](https://git.kicad99.com/bfdx/bf8100-web/commit/de16bfa46422b7f9975c8a6027354c3ac4cda51a))
* 禁止更改prochat网关终端类型和dmrId ([2a651b4](https://git.kicad99.com/bfdx/bf8100-web/commit/2a651b410bcfa89d2e8ce473bf60a25e8d682d57))

## [2.56.3](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.56.2...v2.56.3) (2024-01-16)


### Bug Fixes

* 将等待函数完成的函数改为睡眠函数 ([129738b](https://git.kicad99.com/bfdx/bf8100-web/commit/129738b7f24f56405c85fd94342762e679b3b5c1))
* 将等待函数添加到bfutil，对一些异步函数进行try catch处理 ([eb9da69](https://git.kicad99.com/bfdx/bf8100-web/commit/eb9da69713c38f2350ecd1577df96f5544385280))
* 录音下载时对返回的数据为空的处理，添加翻译,删除log ([41148fe](https://git.kicad99.com/bfdx/bf8100-web/commit/41148fea3fdfb205991287569b12fa2819968a9b))

## [2.56.2](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.56.1...v2.56.2) (2024-01-11)


### Bug Fixes

* 修复BP660写频页面与协议中读取和写入数据的顺序问题,修复协议定义错误问题,处理按键定义的预设信道当信道Id为0xFFFF时的情况 ([6f00857](https://git.kicad99.com/bfdx/bf8100-web/commit/6f0085760e9304ddce7395f2cc5aed8e09598f4c))
* 修复BP660写频页面在读数据时按键定义中预定义功能匹配不到联系人,禁用选择非本系统联系人、信道设置中扫描/漫游列表中扫描漫游数组id为同一个的处理 ([4c99fb6](https://git.kicad99.com/bfdx/bf8100-web/commit/4c99fb6594fbe4324eeadc6795842410302510b6))
* 修复BP660写频页面在读数据时按键定义中预定义功能匹配信道和信道区域的问题 ([47e8b1e](https://git.kicad99.com/bfdx/bf8100-web/commit/47e8b1e72d560a67085e807125577f9eb65cb716))
* 修复BP660写频页面在读数据时表单校验缺失或名称不同，修复写如数据是，协议出现问题的bug ([c3bb16a](https://git.kicad99.com/bfdx/bf8100-web/commit/c3bb16a4ea8db2989eaacb5beaf4d916939934f8))
* 修复BP660写频页面在读数据时遇到的bug问题 ([2523126](https://git.kicad99.com/bfdx/bf8100-web/commit/2523126fc9ca916ed15fb48d0760199853fb262c))
* 修复BP660写频页面通讯录勾选联系人和读取到的联系人合并为一个数组时出现重复联系人导致渲染报错 ([51871b6](https://git.kicad99.com/bfdx/bf8100-web/commit/51871b648c2059a004455d30df8b76cda856a359))
* 修复deviceInfo的父组件获取的设备信息对象为空 ([e3bb801](https://git.kicad99.com/bfdx/bf8100-web/commit/e3bb801d4b27a0a4fb93713f08a0bf953b1d6093))
* 修复扫描组漫游组等可以切换展示的模板上，悬停展示删除按钮 ([19df051](https://git.kicad99.com/bfdx/bf8100-web/commit/19df051244888c94ae0c2cd4d536caed58a1e8b9))
* 修复漫游组移动至删除按钮，不显示删除按钮 ([b9437b2](https://git.kicad99.com/bfdx/bf8100-web/commit/b9437b2515198078ad3826e094571a3dffed5381))
* 修改协议中别名开关使能的字节位置 ([b5135b3](https://git.kicad99.com/bfdx/bf8100-web/commit/b5135b3433ea40fbb5f79d5110d56876825bb5f7))
* 删除不使用的方法 ([1738302](https://git.kicad99.com/bfdx/bf8100-web/commit/1738302c5cfc62201fbe51239ea508c60daf220c))
* 完成BP660对讲机写频页面信道配置界面的相关设置 ([c607898](https://git.kicad99.com/bfdx/bf8100-web/commit/c607898fc577f6189c3cc007a03c2de52ed3916f))
* 完成BP660对讲机写频页面信道配置界面的相关设置 ([8e0130f](https://git.kicad99.com/bfdx/bf8100-web/commit/8e0130f1571593218c9841cc798199a11490bf70))
* 完成BP660对讲机写频页面相关设置 ([f19e26d](https://git.kicad99.com/bfdx/bf8100-web/commit/f19e26d8a8eca69ac36b2f51faa7dfd147f33e32))
* 完成BP660对讲机写频页面除信道配置和录音以外的界面相关设置 ([f0a24d5](https://git.kicad99.com/bfdx/bf8100-web/commit/f0a24d57260812f8ccbe0580f771e75e13f7472e))
* 完成对BP660对讲机写频界面的加密配置、菜单定义、卫星定位设置、信令系统界面相关配置 ([cc4239b](https://git.kicad99.com/bfdx/bf8100-web/commit/cc4239ba51e6215e4ab0c0d9c722761e1220d7bf))
* 完成对BP660对讲机写频界面的警报、通讯录接收组、信道设置、扫描、漫游界面的相关配置 ([b187ed0](https://git.kicad99.com/bfdx/bf8100-web/commit/b187ed0667078cc6b7389469b294475703939a4e))
* 完成对BP660对讲机写频界面的设备信息、常规设置、按键定义界面相关配置 ([cfc5aaf](https://git.kicad99.com/bfdx/bf8100-web/commit/cfc5aaffb5801e3ec824088ec20c2cf9538a6f2a))
* 对录音文件下载的应答判断，修改通讯录群组的组件，额外添加漫游菜单配置 ([3612f74](https://git.kicad99.com/bfdx/bf8100-web/commit/3612f7466b6241ac799b4fc3b61555888c245bbd))
* 对按键定义更新协议的相关进行内容更改 ([d3a5c90](https://git.kicad99.com/bfdx/bf8100-web/commit/d3a5c90a6df4876015b0184803bd7460781a6b09))
* 对读取到的录音文件列表的目标Id和时间进行处理 ([ab0753f](https://git.kicad99.com/bfdx/bf8100-web/commit/ab0753f0f115f40cf339307a1b4531ea020e0199))
* 对读取数据时同步的通讯录群组的已选联系人进行分组处理 ([4b510ee](https://git.kicad99.com/bfdx/bf8100-web/commit/4b510ee6c660f6941d11610fe000d98c6ec2bdd2))
* 录音页新增读取录音文件列表功能-单独读取录音文件列表 ([25f2fcd](https://git.kicad99.com/bfdx/bf8100-web/commit/25f2fcda773cfe8d4fb06d774431b0fb7af11523))
* 恢复默认型号 ([8023da8](https://git.kicad99.com/bfdx/bf8100-web/commit/8023da8dc4ecda19a3ef5366d03c085476f2125b))
* 更新BP660.js协议，并对更新协议相关的660写频页面，按键定义与菜单配置 ([76f9da9](https://git.kicad99.com/bfdx/bf8100-web/commit/76f9da95c00164af18aea021db553e709fa5b418))
* 添加BP660写频页面使用datatables.vue页面的适配参数 ([c870278](https://git.kicad99.com/bfdx/bf8100-web/commit/c87027816c3ae76e0a084f8ac68b184cecf05281))
* 添加下载录音文件对应方法，下发V指令进入传输模式 ([eb0880b](https://git.kicad99.com/bfdx/bf8100-web/commit/eb0880b3bd2d4b8f35161c6160d919b62d83a2a4))
* 添加录音文件信息协议与下载录音文件逻辑代码 ([b882ab4](https://git.kicad99.com/bfdx/bf8100-web/commit/b882ab488fd9ed139e7fd2b5b308a6618d3e2191))
* 添加录音文件表格复选框代理事件 ([80aba81](https://git.kicad99.com/bfdx/bf8100-web/commit/80aba810aba286a46978968403ac3b5e1331fa19))
* 添加虚拟集群默认参数 ([09f57ae](https://git.kicad99.com/bfdx/bf8100-web/commit/09f57aeded317674f9f160c293b5cc581b4b1dac))
* 读取到录音文件数据，对读取到的录音文件数据进行单独处理 ([87b1f64](https://git.kicad99.com/bfdx/bf8100-web/commit/87b1f647f95c4d064577b94bfb4eadac0b1588c4))
* 读取单个录音文件的数据并解码下载 ([6730262](https://git.kicad99.com/bfdx/bf8100-web/commit/67302621ea3e9ac942a01a2474e36a1fa46641e1))

## [2.56.1](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.56.0...v2.56.1) (2023-12-04)


### Bug Fixes

* 更新res_repeater_state的驻波值状态 ([e9b4399](https://git.kicad99.com/bfdx/bf8100-web/commit/e9b4399ab19ac6a8e21be2666c0571decc9def1a))
* 添加中继状态的驻波值参数 ([60e729c](https://git.kicad99.com/bfdx/bf8100-web/commit/60e729c5ffeca02de9cf671e0e01f6b1bcda4363))

## [2.56.0](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.55.0...v2.56.0) (2023-11-28)


### Features

* add addedProchatDevice init func and change update func ([e0c1883](https://git.kicad99.com/bfdx/bf8100-web/commit/e0c18832e692d4b4e255e17ca77b42f40e0a4116))


### Bug Fixes

* change init gaddedProchatDevice func execution position ([9812707](https://git.kicad99.com/bfdx/bf8100-web/commit/9812707c6ede4acb0b71fdd465943ed83c80426e))
* change prochatControllerRid to prochatUserRid ([8196b42](https://git.kicad99.com/bfdx/bf8100-web/commit/8196b4232fc6a87c0a5ee6b2fde7cc9fb4787a79))
* delete initGprochatDeviceInfoList ([7140710](https://git.kicad99.com/bfdx/bf8100-web/commit/7140710a75e0bb70ffbc7f4e28cd73a683157d6f))
* delete unused variable ([aee28d0](https://git.kicad99.com/bfdx/bf8100-web/commit/aee28d07efc3d299e47dc2d5b62c6692ef0591e2))

## [2.55.0](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.54.4...v2.55.0) (2023-11-17)


### Features

* 在添加公网终端时制定专属用户，不允许更改 ([d46f3e5](https://git.kicad99.com/bfdx/bf8100-web/commit/d46f3e58b4fd037b0653a4a3fdd15170d9fc2fd1))
* 添加 gaddedProchatDevice，一对一映射 msID 和 DmrID ([7d379ae](https://git.kicad99.com/bfdx/bf8100-web/commit/7d379ae23cf4a150fd5fc319880a7acc517ca4d3))
* 添加 prochat.proto ([959e93a](https://git.kicad99.com/bfdx/bf8100-web/commit/959e93a06261b8dbb4003f4acafe0518413993d4))
* 添加 prochatDeviceSelect 组件 ([3e3910f](https://git.kicad99.com/bfdx/bf8100-web/commit/3e3910ffdb066f864d616b1a8f9f0c04d2d39e90))
* 添加 prochatDeviceSelect 组件 ([5990c70](https://git.kicad99.com/bfdx/bf8100-web/commit/5990c702c4f36f48d5d5ea3e5d7eb486ef70a152))
* 添加 prochatrpc.proto ([0e00182](https://git.kicad99.com/bfdx/bf8100-web/commit/0e00182fd537ab6e9ff74ff27c17c4f846435e43))


### Bug Fixes

* simplify get deviceUser rid method ([01acfec](https://git.kicad99.com/bfdx/bf8100-web/commit/01acfec9584905c25efb8edef0f6fe5d22f2cc85))

## [2.54.4](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.54.3...v2.54.4) (2023-11-16)


### Bug Fixes

* 修正检测新版功能模块导入i18n路径错误 ([1237047](https://git.kicad99.com/bfdx/bf8100-web/commit/1237047e9a8fc113d2ac1cd4fc606dc6e7c5dcd1))

## [2.54.3](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.54.2...v2.54.3) (2023-11-16)


### Bug Fixes

* 添加检测新版UI功能 ([15355c4](https://git.kicad99.com/bfdx/bf8100-web/commit/15355c47fd238f26153782860cac2124379c4a08))

## [2.54.2](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.54.1...v2.54.2) (2023-11-15)


### Bug Fixes

*  修复TD920常规设置的U盘模式密码没有初始化，可能导致密码输入异常问题 ([34aed7e](https://git.kicad99.com/bfdx/bf8100-web/commit/34aed7e2209543b08a9dee02eb75a59e60fb9bc9))

## [2.54.1](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.54.0...v2.54.1) (2023-11-14)


### Bug Fixes

* 修复常规设置密码配置项在读取后的密码模式处理 ([8bc2f81](https://git.kicad99.com/bfdx/bf8100-web/commit/8bc2f814519b1d0a2302079b5d1dcfa02f594ec3))
* 修复常规设置密码配置项在读取后的密码模式处理 ([77ff619](https://git.kicad99.com/bfdx/bf8100-web/commit/77ff619b55862c64ed1dc14dce0fba5d6ad75dd0))
* 修复测试TD920写频页面提交的BUG;格式化文件TD920.vue文件 ([81b3297](https://git.kicad99.com/bfdx/bf8100-web/commit/81b32978f4ed488f091d0c4de56cd068e0f3f0ef))
* 修改英文翻译 ([3f19abe](https://git.kicad99.com/bfdx/bf8100-web/commit/3f19abe56f2dcc46371b322239e90fa34c1b2c6f))

## [2.54.0](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.53.0...v2.54.0) (2023-11-13)


### Features

* 升级vue-cli版本为5版本 ([b88f2b2](https://git.kicad99.com/bfdx/bf8100-web/commit/b88f2b2317b2e0e5ed146c7282541b6e10e10a99))
* 废弃build.config.js，改用env环境变量和publicPath来处理资源路径问题 ([864dbd2](https://git.kicad99.com/bfdx/bf8100-web/commit/864dbd2f807209b191464ce2bac26057e7ae4ba3))


### Bug Fixes

* 优化vue-cli5的eslint插件参数 ([75c80a3](https://git.kicad99.com/bfdx/bf8100-web/commit/75c80a3fcf3469d146c2ed21f0e58457a15d0209))
* 修改环境变量配置 ([6e06ecd](https://git.kicad99.com/bfdx/bf8100-web/commit/6e06ecd0e51285f0657225e23b3b742f98cadbfe))
* 修改组件路径命令，适配vue-cli5开发工具的环境 ([5862184](https://git.kicad99.com/bfdx/bf8100-web/commit/5862184fa98ba1558c9105ad7769af43177654e7))
* 只打包支持的语言包 ([630ea69](https://git.kicad99.com/bfdx/bf8100-web/commit/630ea69ca8ecaa99eab0232bffedbf72fdc44487))

## [2.53.0](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.52.0...v2.53.0) (2023-11-11)


### Features

* TD920的巡逻系统和求救/救援配置调整 ([4f9d06c](https://git.kicad99.com/bfdx/bf8100-web/commit/4f9d06c2f5786be4952a117bb22d70ca57d329aa))
* 以TD910(SDC)为模板,添加TD920机型写频页面 ([387b291](https://git.kicad99.com/bfdx/bf8100-web/commit/387b291f2cdfe203667fed9e7ef90dddd619c269))
* 修改TD920信道配置页面 ([1a98eef](https://git.kicad99.com/bfdx/bf8100-web/commit/1a98eef8d6fb442120dbc891dab67b6a66dbb721))
* 修改适配TD920的设备信息 ([6407699](https://git.kicad99.com/bfdx/bf8100-web/commit/6407699e49540f4e810e4017583cdaa6b94a1670))
* 同步TD920按键定义的预设信道列表 ([95c9295](https://git.kicad99.com/bfdx/bf8100-web/commit/95c929558ab10305b484599f524c5b1dcc6ea94f))
* 完成TD920加密配置页面修改 ([9cb2cc5](https://git.kicad99.com/bfdx/bf8100-web/commit/9cb2cc551ed5218e1343e6ef79f4b7945d53e388))
* 完成TD920卫星定位设置和巡逻系统节点新增和删除 ([63d2594](https://git.kicad99.com/bfdx/bf8100-web/commit/63d2594010592f446155147b71881eeda4571345))
* 完成TD920卫星定位设置页面修改 ([3983929](https://git.kicad99.com/bfdx/bf8100-web/commit/3983929549fc0f57704dd64e8aa32e66f660ea83))
* 完成TD920扫描组页面修改 ([2c64585](https://git.kicad99.com/bfdx/bf8100-web/commit/2c64585261101006e6883afb2e62fdb2b1699991))
* 完成TD920数字紧急警报页面修改 ([5d75261](https://git.kicad99.com/bfdx/bf8100-web/commit/5d752613e4890f759ffe69ea4b0a01bc9b20ca12))
* 完成TD920的常规设置页面修改 ([ffd38e3](https://git.kicad99.com/bfdx/bf8100-web/commit/ffd38e3416a837f74e6bab3acd9464f58ec43ba8))
* 完成TD920的按键定义页面修改 ([4a9ecf0](https://git.kicad99.com/bfdx/bf8100-web/commit/4a9ecf04dbc74bbd62550a54837ab0f35123edb7))
* 完成TD920菜单配置页面修改 ([007fffc](https://git.kicad99.com/bfdx/bf8100-web/commit/007fffce150e11c3ee7045ef4eae1ad6e7b9e40a))
* 完成TD920警报设置页面修改 ([9e5eb0d](https://git.kicad99.com/bfdx/bf8100-web/commit/9e5eb0d81c5a041cd7c372c3f06471701b7e664a))
* 添加TD920机型协议配置模块 ([ce96db7](https://git.kicad99.com/bfdx/bf8100-web/commit/ce96db7635cefd88355d1d5c9b4b217fa28c6c55))
* 添加TD920机型协议配置模块 ([92acb1f](https://git.kicad99.com/bfdx/bf8100-web/commit/92acb1f8c8234eb430f97c35054af6bdc6075658))
* 添加TD920机型的机型码配置 ([ac4278f](https://git.kicad99.com/bfdx/bf8100-web/commit/ac4278f77a8481bf7940baa32f3ea0ffcb65b1bb))
* 添加TD920机型终端信道区域配置 ([80ad2db](https://git.kicad99.com/bfdx/bf8100-web/commit/80ad2db0159725ed5fd1c4d7c4abc9fb31fa2a42))


### Bug Fixes

* TD920写频漫游配置添加"自动搜索计时器"参数 ([0c585b8](https://git.kicad99.com/bfdx/bf8100-web/commit/0c585b86a3f75b34ede480ee3b182b8a21a1afcc))
* 优化TD920的按键定义与短信、通讯录的同步逻辑 ([114d079](https://git.kicad99.com/bfdx/bf8100-web/commit/114d0792e3f20e5e76ef731adc9f1495c625aaea))
* 修复TD920写频常规设置的卫星定位参数和信道加密的密钥列表显示异常 ([3f29880](https://git.kicad99.com/bfdx/bf8100-web/commit/3f2988014c612373c038782502077e3eef872082))
* 修复TD920机型写入数据参数顺序异常问题 ([c2efeb1](https://git.kicad99.com/bfdx/bf8100-web/commit/c2efeb17de36c66025ed595a83744f157e15c817))
* 修复TD920机型漫游组参数的默认值 ([6afa5e6](https://git.kicad99.com/bfdx/bf8100-web/commit/6afa5e60b988178875166bc06c0df0c1b529bd03))
* 修复TD920机型紧急报警参数的默认值 ([95082b7](https://git.kicad99.com/bfdx/bf8100-web/commit/95082b71550a51c7bb8fa7cfc6a57e69448d10c3))
* 修复TD920机型联系人群组分组显示 ([efd229a](https://git.kicad99.com/bfdx/bf8100-web/commit/efd229a0a1ba9368c63743bdbea6928cc3d87b6d))
* 修复TD920机型读取时间设置时判断是否使用本机时间 ([d6a83b3](https://git.kicad99.com/bfdx/bf8100-web/commit/d6a83b326dbd5616c0ab7fe8f96bd4d8005b628b))
* 修复TD920机型读取资源版本信息 ([00b8210](https://git.kicad99.com/bfdx/bf8100-web/commit/00b821095594fd2c414417b43e80c7919ffd1ec0))
* 修复TD920机型读取通讯录信息判断呼叫类型时判断错误 ([4b8fd97](https://git.kicad99.com/bfdx/bf8100-web/commit/4b8fd976a882ff5c8cc44fd43b46755838cea925))
* 修复TD920机型读取通讯录信息判断呼叫类型时判断错误 ([4cb82a6](https://git.kicad99.com/bfdx/bf8100-web/commit/4cb82a64b96c2428447593e1a8ae09f2ccc10469))
* 修复TD920机型读取通讯录群组时显示不完整 ([b73f6ea](https://git.kicad99.com/bfdx/bf8100-web/commit/b73f6ea9a9069cf6ecac645af7c31761ff0926d5))
* 修复TD920机型通讯录取消勾选时通讯录群组没有对应的取消已选联系人;去除表单内的size属性 ([776684f](https://git.kicad99.com/bfdx/bf8100-web/commit/776684f9b3f766d4ee3f3bf8bd613fdce2ca2527))
* 修复加密配置组件在初始化时重复生成密钥异常 ([2baedb1](https://git.kicad99.com/bfdx/bf8100-web/commit/2baedb1caca1cdd8c2df2803662b970bcfb41054))
* 修复加密配置组件的密钥删除按钮禁用状态不对问题，并添加密钥允许输入正则参数 ([517647a](https://git.kicad99.com/bfdx/bf8100-web/commit/517647ab61cb53c0fb42d18ac667c114c82b7579))
* 修改TD920常规设置的语言默认参数 ([9bd61c6](https://git.kicad99.com/bfdx/bf8100-web/commit/9bd61c611d2110a2761ae8d799d295fc3f102342))
* 修改TD920机型写频信令系统页面，添加一些缺失的翻译 ([868b7e7](https://git.kicad99.com/bfdx/bf8100-web/commit/868b7e734f236f95c2f406ea00a9255a75fc1437))
* 修改TD920机型菜单配置部分参数的默认值 ([10c139b](https://git.kicad99.com/bfdx/bf8100-web/commit/10c139baedfbae669aea19f37871e2cf069668ac))
* 修改TD920紧急报警联系人默认参数文本 ([8b77aed](https://git.kicad99.com/bfdx/bf8100-web/commit/8b77aed7c5c65efedbab089825c1a8805d5842ff))
* 修改TD920跟踪监控参数名称错误 Changes by gz8 ([0847a52](https://git.kicad99.com/bfdx/bf8100-web/commit/0847a52521ebc0d0e0fdb8dc146524b70cf75a65))
* 删除TD920写频el-form-item表单元素下的尺寸属性 ([351e11d](https://git.kicad99.com/bfdx/bf8100-web/commit/351e11dfebeabccea5ec12106bbee2ef43efe3d3))
* 加密配置组件加载时,默认生成一条密钥 ([4e86a51](https://git.kicad99.com/bfdx/bf8100-web/commit/4e86a516865ebc405ffa672808d327f136f55e1e))
* 完善TD920菜单配置与设备可选功能关系 ([0627241](https://git.kicad99.com/bfdx/bf8100-web/commit/062724182d87b331e74ea7c80f49b3d35a2a106d))
* 测试时异常处理 ([b37c3bd](https://git.kicad99.com/bfdx/bf8100-web/commit/b37c3bd28519e7195d52e08059aa610f6bfbbca1))
* 添加TD920机型读取、写入缺失的功能逻辑 ([bb0f38f](https://git.kicad99.com/bfdx/bf8100-web/commit/bb0f38ff784e476a0cf2fdd7881cbb9f9eeaf4c9))
* 规避TD920写扫描组默认对象计算属性无法正确触发问题 ([8d605f3](https://git.kicad99.com/bfdx/bf8100-web/commit/8d605f3b77b72138bcffd9764f30f0368f3f5ade))
* 设置TD920警报设置页面属性名称一致 ([c154df1](https://git.kicad99.com/bfdx/bf8100-web/commit/c154df1b5ab830216e4f265a1ac0af218fd2f6ca))
* 设置TD920警报设置页面属性名称一致 ([e201a05](https://git.kicad99.com/bfdx/bf8100-web/commit/e201a05cb6a27abf7f8f883ca956d35a2c875010))
* 调试TD920写频功能 ([01bd738](https://git.kicad99.com/bfdx/bf8100-web/commit/01bd73826e1453b9b9f72c7e1786325464269041))

## [2.52.0](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.51.13...v2.52.0) (2023-11-10)


### Features

* 实现 prochatGataway 添加和删除功能 ([36d4602](https://git.kicad99.com/bfdx/bf8100-web/commit/36d4602a8ae5bf5c6151a46ca27f803720096887))
* 实现 prochatGateway 的 update 功能，同时更新 prochat 用户和 prochat网关终端 ([ef1c384](https://git.kicad99.com/bfdx/bf8100-web/commit/ef1c384d6a548f4d8a4e3f9c420b8dc7696049b7))
* 禁止在终端管理页面更改 prochat网关终端类型和dmrId ([8dc1e39](https://git.kicad99.com/bfdx/bf8100-web/commit/8dc1e393d2f14f377097c691e69d56d98351d2c9))
* 禁止用户管理页面删除 prochat 用户，禁止终端管理页面删除 prochatGatewayDevice ([1ab6cc1](https://git.kicad99.com/bfdx/bf8100-web/commit/1ab6cc1f6a496c49c1d2c9abec79b70240c0261b))
* 禁止用户管理页面更新 prochat 用户的自编号 ([219e6f0](https://git.kicad99.com/bfdx/bf8100-web/commit/219e6f001c048b0e959855bea4517f4e6acaa540))


### Bug Fixes

* 设备管理页面选择 Prochat 网关类型后，点击设置按钮时 prochatGatewaySetting 页面闪烁 ([50e0a9b](https://git.kicad99.com/bfdx/bf8100-web/commit/50e0a9baf96653bcd6ef9505669b6137b6aa0fdc))

## [2.51.13](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.51.12...v2.51.13) (2023-10-26)


### Bug Fixes

* 修复TD910/TD910P菜单选项的时隙参数协议偏移量属性错误 ([6d9162c](https://git.kicad99.com/bfdx/bf8100-web/commit/6d9162ce2c5c1b2a7c5814b4e1e02e1ada347bc5))
* 修复查询图片请求返回空数据,导致其他请求无法正常执行的错误 ([84739dd](https://git.kicad99.com/bfdx/bf8100-web/commit/84739dd1f888fcd5fb2284993626320dbd1f1730))
* 在紧急报警窗口中,下发解除报警命令,在5秒内没有收到应答,则自动重发命令3次 ([362b241](https://git.kicad99.com/bfdx/bf8100-web/commit/362b241502f9cc6e1ada0042108a00be337587ef))

## [2.51.12](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.51.11...v2.51.12) (2023-10-11)


### Bug Fixes

* 修复同频同播中继重新注册时，没有更新状态监控信息异常 [release] ([c5100ee](https://git.kicad99.com/bfdx/bf8100-web/commit/c5100eec99d4b438f8c27fe52fee4276d4a05713))

## [2.51.11](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.51.10...v2.51.11) (2023-10-09)


### Bug Fixes

* 修复虚拟集群中继在上线时，没有修正支持的功能，导致无法显示状态监控查询按钮问题 [release] ([d1ad269](https://git.kicad99.com/bfdx/bf8100-web/commit/d1ad2699d1b79c085d203f014896bb6ffab62b1a))

## [2.51.10](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.51.9...v2.51.10) (2023-09-22)


### Bug Fixes

* 调整中继状态窗口的中继信息文本样式，最大2行显示 ([4f8dce4](https://git.kicad99.com/bfdx/bf8100-web/commit/4f8dce48b6a70674427ac3d9b2823fbfaabea583))

## [2.51.9](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.51.8...v2.51.9) (2023-09-22)


### Bug Fixes

* 修复中继重新上线后，没有成功订阅状态事件异常 ([ff1ee60](https://git.kicad99.com/bfdx/bf8100-web/commit/ff1ee6061d60fbb5860ec574d06b6a457dbeb253))

## [2.51.8](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.51.7...v2.51.8) (2023-09-22)


### Bug Fixes

* 修复取消订阅中继状态事件错误参数，导致nats解析出错，无法正常处理后续消息 ([ee31c76](https://git.kicad99.com/bfdx/bf8100-web/commit/ee31c76654c96130295693a670523271638ecdd6))
* 修改中继状态tooltip提示，改用原生的title属性，添加刷新状态按钮 ([2b4174f](https://git.kicad99.com/bfdx/bf8100-web/commit/2b4174fd55e23c285a39815e626e328d04de25be))
* 给中继状态刷新按钮添加title属性 ([4a850cc](https://git.kicad99.com/bfdx/bf8100-web/commit/4a850ccb0b067fea4fb99656bc101b2769b43a61))

## [2.51.7](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.51.6...v2.51.7) (2023-09-15)


### Bug Fixes

* 修复设备管理的中继状态监控不支持同频同播中继和虚拟集群中继问题，并过滤上级控制器 ([b4c202f](https://git.kicad99.com/bfdx/bf8100-web/commit/b4c202f7c3eb25a1610c62d2e23306b464e50bd1))

## [2.51.6](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.51.5...v2.51.6) (2023-09-15)


### Bug Fixes

* 中继状态对话框,挂载到body元素上 ([b611629](https://git.kicad99.com/bfdx/bf8100-web/commit/b611629506269d1686669e94ae495bab265856ff))
* 中继状态添加单位和设备名称信息,调整状态栏样式,统一对齐 ([69cbe0d](https://git.kicad99.com/bfdx/bf8100-web/commit/69cbe0de749e8e8d9cd679fa4579860d8bb430c1))

## [2.51.5](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.51.4...v2.51.5) (2023-09-14)


### Bug Fixes

* 中继状态对话框,挂载到body元素上 ([d0e8985](https://git.kicad99.com/bfdx/bf8100-web/commit/d0e89857669ba40ea6dfb0dc1558894aa0755b58))
* 中继状态添加单位和设备名称信息,调整状态栏样式,统一对齐 ([ec2e0e4](https://git.kicad99.com/bfdx/bf8100-web/commit/ec2e0e4a7a260cf316519f9c92b894784f514b69))

## [2.51.4](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.51.3...v2.51.4) (2023-09-14)


### Bug Fixes

* 修复中继状态管理信道翻译方法调用错误 ([83c611b](https://git.kicad99.com/bfdx/bf8100-web/commit/83c611b234873ee79d3f8ca3c0989e4c00957b8b))
* 修复中继状态管理信道翻译方法调用错误 ([b38943d](https://git.kicad99.com/bfdx/bf8100-web/commit/b38943d8683e36bff2b8bf54d3320d6c7fcf4078))

## [2.51.3](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.51.2...v2.51.3) (2023-09-14)


### Bug Fixes

* 修复中继状态管理信道翻译方法调用错误 ([7047bf5](https://git.kicad99.com/bfdx/bf8100-web/commit/7047bf5c68b00b36de305fb9c0270ac79dec476b))

## [2.51.2](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.51.1...v2.51.2) (2023-09-11)


### Bug Fixes

* 中继状态监控定时查询的定时器调整为30s一次 ([514c8e2](https://git.kicad99.com/bfdx/bf8100-web/commit/514c8e2e0f94f71c48084732d579d62fc4696c07))
* 中继状态监控定时查询的定时器调整为30s一次，并添加状态刷新时间 ([76ad38e](https://git.kicad99.com/bfdx/bf8100-web/commit/76ad38e4d31325904bca2d832e433212aa91a7db))
* 完善中继状态监控在服务器断开和连接时，定时器暂停和启动逻辑 ([9e05d6a](https://git.kicad99.com/bfdx/bf8100-web/commit/9e05d6a222537ce367dd51f93c0c673ffe80a5f7))

## [2.51.1](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.51.0...v2.51.1) (2023-09-08)


### Bug Fixes

* TD930, BP750机型添加新的6位机型码 ([ba12d45](https://git.kicad99.com/bfdx/bf8100-web/commit/ba12d45a6ed5841894afd4af7267b23a29c08501))

## [2.51.0](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.50.10...v2.51.0) (2023-09-08)


### Features

* 完善中继状态监控 ([1bcd17c](https://git.kicad99.com/bfdx/bf8100-web/commit/1bcd17c170ba6cc3fadc51cb7fa13e21b0924eff))
* 更新中继警报状态协议 ([025f991](https://git.kicad99.com/bfdx/bf8100-web/commit/025f991139341d6176d601e1f6814250ad10c589))
* 添加mdi字体图标库 ([feeca99](https://git.kicad99.com/bfdx/bf8100-web/commit/feeca991954b96c18323cd636cba5dcbe7e3ae55))
* 添加中继状态监控协议 ([1dd3882](https://git.kicad99.com/bfdx/bf8100-web/commit/1dd38829192b12528c5069a779f78df08ea8eab8))
* 添加中继状态页面，显示种类状态 ([2edffe6](https://git.kicad99.com/bfdx/bf8100-web/commit/2edffe61f0dd9c249d141038189404326397f113))
* 设备管理页面，添加订阅中继状态变更和查询事件 ([9bf5365](https://git.kicad99.com/bfdx/bf8100-web/commit/9bf5365b7e9fb3122df188bd33dc0391d941358b))


### Bug Fixes

* 使用'NODE_ENV'来判断日志打开级别 ([0e570ef](https://git.kicad99.com/bfdx/bf8100-web/commit/0e570ef3eba484ff742c892779ba64673e66f69a))

## [2.50.10](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.50.9...v2.50.10) (2023-09-05)


### Bug Fixes

* 修复了缓存语言失败 ([85d2f12](https://git.kicad99.com/bfdx/bf8100-web/commit/85d2f12bd1b2d562785ca76fa69428f6779668c6))

## [2.50.9](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.50.8...v2.50.9) (2023-08-22)


### Bug Fixes

*  锁定qwebchannel版本为5.9.0 ([bbc9bfd](https://git.kicad99.com/bfdx/bf8100-web/commit/bbc9bfd420126c7cbaf3ad5af6f69ac244264086))

## [2.50.8](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.50.7...v2.50.8) (2023-08-17)


### Bug Fixes

* fix datatables buttons props default value error and fix sort icon style ([e12a207](https://git.kicad99.com/bfdx/bf8100-web/commit/e12a2076ef96316c9774ac55451aea0bd4a3a3e6))
* upgrade packages ([a0e4c3a](https://git.kicad99.com/bfdx/bf8100-web/commit/a0e4c3ad5bc4f90e01fc7dadc624bac02d531543))
* upgrade packages ([f920ec1](https://git.kicad99.com/bfdx/bf8100-web/commit/f920ec12b4c371b51878b8b8bf2490ec00991fe4))

## [2.50.7](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.50.6...v2.50.7) (2023-08-16)


### Bug Fixes

* 修改控制器网关类型名称为"电话网关" ([c6b07ba](https://git.kicad99.com/bfdx/bf8100-web/commit/c6b07ba7209073f0f5fb01d9da6404e02622671f))
* 修改电话黑白名单号码输入正则，不能连续输入多个"*"，其他不限制 ([fdbb4da](https://git.kicad99.com/bfdx/bf8100-web/commit/fdbb4da51be848508d5387455024cfd380ff365c))

## [2.50.6](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.50.5...v2.50.6) (2023-08-16)


### Bug Fixes

* 常规DMR终端接入授权信息添加配额上限信息 ([49019f2](https://git.kicad99.com/bfdx/bf8100-web/commit/49019f20c809955bfdf4e3ea189137b1565263eb))
* 添加常规DMR终端接入数量限制 ([df1cfac](https://git.kicad99.com/bfdx/bf8100-web/commit/df1cfac82f568785607c7bd84e246691226d0ae3))

## [2.50.5](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.50.4...v2.50.5) (2023-08-14)


### Bug Fixes

* 修复打开dialog对话框后，一些默认样式与mapbox样式冲突问题 ([1f935ca](https://git.kicad99.com/bfdx/bf8100-web/commit/1f935ca73b98b3fc670f6abb2a5fe229f0e5f37a))

## [2.50.4](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.50.3...v2.50.4) (2023-08-14)


### Bug Fixes

* 修改中继的常规DMR终端接入参数布局及默认值 ([0dd892b](https://git.kicad99.com/bfdx/bf8100-web/commit/0dd892bf42b771289bc34c22832d2c0996de8469))

## [2.50.3](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.50.2...v2.50.3) (2023-08-10)


### Bug Fixes

*  修复SnNumber模块初始化和转换过程中没有正确处理c#的char类型转换错误 ([59aa732](https://git.kicad99.com/bfdx/bf8100-web/commit/59aa732c11da1eda173296b2583645c650ba9f01))
* 修改中继SN解码逻辑，与终端的SN 规则相同 ([fa811be](https://git.kicad99.com/bfdx/bf8100-web/commit/fa811be9d541e800801548a3aca69d7211aca9ef))

## [2.50.2](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.50.1...v2.50.2) (2023-08-08)


### Bug Fixes

* 常规DMR终端接入，不支持动态组命令 ([d13eca0](https://git.kicad99.com/bfdx/bf8100-web/commit/d13eca0b0518e225cef86d99fda2c25c26579dc5))

## [2.50.1](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.50.0...v2.50.1) (2023-08-07)


### Bug Fixes

* 格式化命令号模块文件 ([6dd98b2](https://git.kicad99.com/bfdx/bf8100-web/commit/6dd98b2e36193a33de90b9008c000c2110c938c0))

## [2.50.0](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.49.13...v2.50.0) (2023-08-07)


### Features

* 完成设备管理允许常规DMR终端接入配置逻辑 ([7f5fc5c](https://git.kicad99.com/bfdx/bf8100-web/commit/7f5fc5cf324a02f397c4b5acaa8bd31f57fc1eeb))
* 封装选择接收组组件 ([26f877e](https://git.kicad99.com/bfdx/bf8100-web/commit/26f877ea454a58e3b32b17c1ee9ce80e88c28053))
* 常规DMR终端类型不参与除语音外其他命令 ([46ee624](https://git.kicad99.com/bfdx/bf8100-web/commit/46ee624c6b206e34ff392b375db91b38746bab40))
* 常规DMR终端类型默认允许联网呼叫 ([f72b749](https://git.kicad99.com/bfdx/bf8100-web/commit/f72b7499b895413e393f4789b78bbdeaed1596d8))
* 控制器添加常规DMR终端接入UI页面 ([a21ed70](https://git.kicad99.com/bfdx/bf8100-web/commit/a21ed703f4fd06dbe30c13b419093bec4991928e))
* 终端管理的常规DMR终端类型需要授权后才显示 ([7c51398](https://git.kicad99.com/bfdx/bf8100-web/commit/7c51398a54f9745b178a7a89f43f83ca90553f5e))


### Bug Fixes

* 修改控制器打开的其他配置窗口样式 ([789029c](https://git.kicad99.com/bfdx/bf8100-web/commit/789029c3e3fae5b6031479d2992af9178f1ac332))
* 修改选择收听组组件默认选中的属性为dmrId ([c28fe50](https://git.kicad99.com/bfdx/bf8100-web/commit/c28fe50faf53d0730eddf5cc588773eed433afb5))

## [2.49.13](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.49.12...v2.49.13) (2023-07-21)


### Bug Fixes

* 修复控制器Mesh网关类型的密码框规则在隐藏时不进行检验 ([3f64d8f](https://git.kicad99.com/bfdx/bf8100-web/commit/3f64d8faf03ccb04be3770b02add9f826e9c80ce))

## [2.49.12](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.49.11...v2.49.12) (2023-07-17)


### Bug Fixes

* 修复终端数据的channel属性在json解析时可能出现的undefined ([58b4de4](https://git.kicad99.com/bfdx/bf8100-web/commit/58b4de4d15325a30e8fc64dbfe80d1bf54be4461))

## [2.49.11](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.49.10...v2.49.11) (2023-07-17)


### Bug Fixes

* 修复终端的信道号排序时，自动生成的channel json数据默认undefined问题 ([ec2d153](https://git.kicad99.com/bfdx/bf8100-web/commit/ec2d1535b2955f6be6c5477484c8472ccb411816))

## [2.49.10](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.49.9...v2.49.10) (2023-07-12)


### Bug Fixes

* BF-TD930(SDC)Plus更名为BP750(SDC)，BF-TD930(SVT)Plus更名为BP750(SVT) ([1eae6ab](https://git.kicad99.com/bfdx/bf8100-web/commit/1eae6abeb24e0ab29c9c47f7216e1fc62992e383))

## [2.49.9](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.49.8...v2.49.9) (2023-07-11)


### Bug Fixes

* Mesh网关控制器添加密码输入 ([ae0391f](https://git.kicad99.com/bfdx/bf8100-web/commit/ae0391f8ea48516ea02d917be61c2388967d8c17))
* Mesh网关控制器添加的单位，不允许用户删除 ([30a6dea](https://git.kicad99.com/bfdx/bf8100-web/commit/30a6deac1c9aef55875a715d2fb88e5207a3b181))
* 修复Mesh网关控制器密码表单验证失败问题 ([a35196c](https://git.kicad99.com/bfdx/bf8100-web/commit/a35196cd922dd902b5ceacdc220bf488127193e0))
* 修改控制器添加Mesh网关失败的消息提示 ([beebbbb](https://git.kicad99.com/bfdx/bf8100-web/commit/beebbbb91dee41967f89a99cb2fca8ddbc4ad9ce))
* 添加Mesh网关控制器时，自动创建对应的单位数据 ([92deda5](https://git.kicad99.com/bfdx/bf8100-web/commit/92deda5519257ce13752e614756c5ee921430f2f))
* 调整带详情的消息的样式 ([57d71c9](https://git.kicad99.com/bfdx/bf8100-web/commit/57d71c9116a935be37afcf1868cfa18d85e24477))

## [2.49.8](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.49.7...v2.49.8) (2023-06-21)


### Bug Fixes

* 终端管理更新页添加适配"无"优先级 ([f5c2525](https://git.kicad99.com/bfdx/bf8100-web/commit/f5c2525d60228d1a46b18c920e028faa51c4263f))

## [2.49.7](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.49.6...v2.49.7) (2023-06-21)


### Bug Fixes

* 控制器创建的终端，SIP网关终端优先级为0，其他默认为低级 ([01188c8](https://git.kicad99.com/bfdx/bf8100-web/commit/01188c82546280610b9d4d1f901d388cc583c4fd))

## [2.49.6](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.49.5...v2.49.6) (2023-06-21)


### Bug Fixes

* 由控制器创建的终端，优先级为低级 ([4a3db91](https://git.kicad99.com/bfdx/bf8100-web/commit/4a3db91b4baa36050b4021cf2e07bf2f7fc9cb4e))

## [2.49.5](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.49.4...v2.49.5) (2023-06-06)


### Bug Fixes

* 修改TD511SDC和SVT的"蓝牙PTT"为"蓝牙PTT保持" ([e4b4742](https://git.kicad99.com/bfdx/bf8100-web/commit/e4b474235915d1d1a979bc7af70f075770a443b1))

## [2.49.4](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.49.3...v2.49.4) (2023-06-05)


### Bug Fixes

* TD511(SDC|SVT)读取机型后，同步对应的蓝牙和录音选配参数 ([8db4da7](https://git.kicad99.com/bfdx/bf8100-web/commit/8db4da78caf7eba15c35dcce057b3c7f0507f812))

## [2.49.3](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.49.2...v2.49.3) (2023-06-05)


### Bug Fixes

* TD511(SDC|SVT)修改机型选配功能参数，添加蓝牙选配功能 ([73ab4af](https://git.kicad99.com/bfdx/bf8100-web/commit/73ab4af425dfdc41fa86ab381ae6c8ffb3f4ca86))

### [2.49.2](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.49.1...v2.49.2) (2023-05-19)


### Bug Fixes

* 接收dc01指令时，检查上次读取时间是否异常，是否需要更新数据库参数 ([f2ac33f](https://git.kicad99.com/bfdx/bf8100-web/commit/f2ac33f35a8240967c828aec2736d4b4f56b13b0))

### [2.49.1](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.49.0...v2.49.1) (2023-05-16)


### Bug Fixes

* TM8250(SDC)R7F写频，卫星定位设置开放时区参数 ([d0cffbf](https://git.kicad99.com/bfdx/bf8100-web/commit/d0cffbf50d3b08ec549b388afb66b4c5ccf73bad))
* TM8250(SDC)R7F写频添加缺失的语言选项翻译 ([a831a20](https://git.kicad99.com/bfdx/bf8100-web/commit/a831a203f3d2f3b323ecc745a4b8814a6eead23f))
* 修复TD930(SDC)Plus和TD930(SVT)Plus漫游组和扫描组加载异常 ([c3cfd96](https://git.kicad99.com/bfdx/bf8100-web/commit/c3cfd96b7fb8dc46cc6c59a7dccdfb11397bbfc4))
* 修复福州研发的机型写频，在读取数据时编程密码错误后没有恢复读取按钮状态错误 ([073e10e](https://git.kicad99.com/bfdx/bf8100-web/commit/073e10e73fac291c56a84e3b5fe30cc0df084059))
* 修正TM8250(SDC)R7F机型卫星定位时区参数设置 ([f6795c4](https://git.kicad99.com/bfdx/bf8100-web/commit/f6795c4233596f3d85464d276c8aa2f2c4c7bcd1))
* 所有机型的加密密钥使用"password"模式，不显示明文密钥 ([45831d0](https://git.kicad99.com/bfdx/bf8100-web/commit/45831d0c6e31c39ecd2208071577e017ed302d45))

## [2.49.0](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.48.8...v2.49.0) (2023-05-12)


### Features

* 以TD930(SDC)R7F机型为模板，添加TM8250(SDC)R7F机型写频功能 ([4b08a9d](https://git.kicad99.com/bfdx/bf8100-web/commit/4b08a9da95a34857cabc2f2333061f1ef1215cbe))
* 以TD930(SDC|SVT)R7F机型为模板，创建TD930(SDC|SVT)Plus机型写频功能 ([1d78d46](https://git.kicad99.com/bfdx/bf8100-web/commit/1d78d46e01f674ac0f68d3aa6b94983fcafe5eaf))


### Bug Fixes

*  修改TM8250(SDC)R7F卫星定位数据协议 ([e4a7c6b](https://git.kicad99.com/bfdx/bf8100-web/commit/e4a7c6b800894c1715788f83a903cae82a35a279))
* 修改TD930(SDC|SVT)R7F机型写频菜单配置，添加"信道锁定"参数 ([cbcb78a](https://git.kicad99.com/bfdx/bf8100-web/commit/cbcb78ac0f7bdf6d46ea142166c49adb8c44ac5c))
* 修正TM8250(SDC)R7F读写频缺失的参数逻辑 ([38f6990](https://git.kicad99.com/bfdx/bf8100-web/commit/38f6990e9f06849d447ab7ecff82046b5c237568))

### [2.48.8](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.48.7...v2.48.8) (2023-05-06)


### Bug Fixes

* TD511(SDC)R7F和TD511(SVT)R7F机型菜单配置版本2添加ID最小显示位数和主界面快捷拨号选项 ([b50bd01](https://git.kicad99.com/bfdx/bf8100-web/commit/b50bd013478ba1eca9d0daa5635dd9847de2103b))
* TD818(SDC)R7F和TD818(SVT)R7F机型菜单配置版本2添加ID最小显示位数和主界面快捷拨号选项 ([ca6ee2f](https://git.kicad99.com/bfdx/bf8100-web/commit/ca6ee2fa20c94898acea72099f496fb314bc9837))
* 修改TD511系列菜单配置的"ID最小显示位数"参数转换计算 ([ac09508](https://git.kicad99.com/bfdx/bf8100-web/commit/ac095083e09251e2f8c525b69fd7fc38b7f5edf1))
* 修改TD511系列菜单配置的"ID最小显示位数"和"主界面快捷拨号"参数布局 ([89657f5](https://git.kicad99.com/bfdx/bf8100-web/commit/89657f55673f4fb1587872b3043e57c3d902121e))

### [2.48.7](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.48.6...v2.48.7) (2023-05-05)


### Bug Fixes

* 修改系统右侧树结构排序规则，终端使用dmrId进行升序排序 ([9e6d20d](https://git.kicad99.com/bfdx/bf8100-web/commit/9e6d20df380a7163d5ffe98b40758c3eef298fdb))

### [2.48.6](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.48.5...v2.48.6) (2023-04-25)


### Bug Fixes

* 修复BF-TD818(SDC)机型码引用错误，导致无法读取设备数据问题 ([3c2ae73](https://git.kicad99.com/bfdx/bf8100-web/commit/3c2ae7314886b9b9ed88babb63e5d8a056f9a0e0))

### [2.48.5](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.48.4...v2.48.5) (2023-04-23)


### Bug Fixes

* 部分机型信道频率映射组件添加"mapping"事件，以修复拥有"SVT站点信息"参数同步异常问题 ([099a43b](https://git.kicad99.com/bfdx/bf8100-web/commit/099a43bbf25fc7f1556c28f175de37839b0d816d))
* 频率组件添加"mapping"事件 ([495738e](https://git.kicad99.com/bfdx/bf8100-web/commit/495738e0118b2405b65becf76aa20a568ab577fa))

### [2.48.4](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.48.3...v2.48.4) (2023-04-23)


### Bug Fixes

* 修复TD930(SVT)系列信道"SVT站点信息"参数在切换信道时被重置异常 ([0d944aa](https://git.kicad99.com/bfdx/bf8100-web/commit/0d944aabbfea23b0f5324f7e20226ada232f6717))
* 将频率组件的部分监听器逻辑改为方法，以解决组件初始化时同步触发input事件导致的副作用 ([caa4aa2](https://git.kicad99.com/bfdx/bf8100-web/commit/caa4aa2749eb6a09965a4295eea778a9937786b1))
* 改用el-input-number封装频率映射组件，以修复输入负数时异常 ([8ac05c8](https://git.kicad99.com/bfdx/bf8100-web/commit/8ac05c8964c1f2d85e44bcebd7c76b1231656f0f))

### [2.48.3](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.48.2...v2.48.3) (2023-04-20)


### Bug Fixes

* 修复TD930(SVT)机型在SVT站点数据变更时，重置信道对应的SVT配置参数错误，频率参数属性名称错误 ([086eac6](https://git.kicad99.com/bfdx/bf8100-web/commit/086eac6bfd9dd72b7d45e5d4ec4a5ae1689901ff))

### [2.48.2](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.48.1...v2.48.2) (2023-04-19)


### Bug Fixes

* 修复TD930(SDC)R7F和TD930(SVT)R7F区域信道扩容后，没有重置后续无效信道参数为0xFF的错误，导致写频软件读取数据异常 ([265c5fb](https://git.kicad99.com/bfdx/bf8100-web/commit/265c5fb7cc763e7f7c59c5ed2a1e73a69d948d41))

### [2.48.1](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.48.0...v2.48.1) (2023-04-19)


### Bug Fixes

* 修复TD930(SVT)R7F机型写频的信道协议参数偏移参数错误问题 ([cf7c59a](https://git.kicad99.com/bfdx/bf8100-web/commit/cf7c59a0a912552fab4d2500cbd1113358722277))

## [2.48.0](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.47.0...v2.48.0) (2023-04-18)


### Features

* 添加TD930(SDV)R7F芯片机型写频功能 ([1aaef2b](https://git.kicad99.com/bfdx/bf8100-web/commit/1aaef2bea869fb3142aa9f74a9505d5117e80dfa))
* 添加TD930(SDV)和TD930(SVT)R7F芯片机型码 ([502a99f](https://git.kicad99.com/bfdx/bf8100-web/commit/502a99ff8370291a97ea84981cc6e977444e71b9))
* 添加TD930(SDV)和TD930(SVT)R7F芯片机型终端管理的区域设置 ([3b62142](https://git.kicad99.com/bfdx/bf8100-web/commit/3b62142132defa8a2131808dcf9f7e3faf2715f5))
* 添加TD930(SVT)R7F机型写频功能 ([1762223](https://git.kicad99.com/bfdx/bf8100-web/commit/1762223c65f5c63b754d001457e3c8f3bb89b823))
* 添加v3版本的序列号算法，向下兼容v2, v1算法 ([1a23cab](https://git.kicad99.com/bfdx/bf8100-web/commit/1a23cabe7ce932983dd07ce13a5bb2652317188b))


### Bug Fixes

* TD930(SDC)R7F的机型码使用常量参数 ([c260657](https://git.kicad99.com/bfdx/bf8100-web/commit/c2606572c2216aeb304aa09583adc66962eb312c))
* 信道频率偏移参数范围添加默认参数 ([230c0ad](https://git.kicad99.com/bfdx/bf8100-web/commit/230c0adcff629bd3a9149953c29d42fb7ccd82a3))
* 修正TD930(SDC)和(SVT)R7F机型区域协议参数错误 ([2b29002](https://git.kicad99.com/bfdx/bf8100-web/commit/2b29002f50217e6eb8093b10e8b1d8ea1ef7df84))
* 所有机型都使用新版本的序列号算法 ([b5b157a](https://git.kicad99.com/bfdx/bf8100-web/commit/b5b157aa8b17e5293460b14651982a35a31bec60))

## [2.47.0](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.46.0...v2.47.0) (2023-04-14)


### Features

* 添加BF-TD818(SDC)机型写频功能，以BF-TD818(SVT)为模板，屏蔽SVT相关配置 ([312ed03](https://git.kicad99.com/bfdx/bf8100-web/commit/312ed03906965d1b3a0611ba71be481737be3407))


### Bug Fixes

* 使用生成的机型模块中的检查机型匹配方法 ([c28ef4f](https://git.kicad99.com/bfdx/bf8100-web/commit/c28ef4fec21a56c1d76f119b4c6a8b553e49fd0b))
* 修改生成机型码信息模块逻辑，添加检查机型是否匹配方法，以兼容部分机型码最后2位不固定时查找机型名称异常问题 ([111bfb3](https://git.kicad99.com/bfdx/bf8100-web/commit/111bfb3469d229bdf889f72ee38c2fed909affc9))

## [2.46.0](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.45.4...v2.46.0) (2023-04-07)


### Features

* 添加BF-TD818(SVT)机型写频功能，以BF-TD511SVT(R7F)为模板，修改模拟信道亚音参数配置 ([33dac2e](https://git.kicad99.com/bfdx/bf8100-web/commit/33dac2e1bfe1e4362211863a6260818a71228fae))

### [2.45.4](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.45.3...v2.45.4) (2023-04-07)


### Bug Fixes

* rpc_cmd消息结构添加opt_int字段 ([fb87256](https://git.kicad99.com/bfdx/bf8100-web/commit/fb87256457280d5f49cb0cc5a17254166d29771a))
* 修改请求地图Tile资源的token请求，使用optInt接收随机数 ([3dc28cb](https://git.kicad99.com/bfdx/bf8100-web/commit/3dc28cb25dfc1508abd65af64dfece41caa514fc))

### [2.45.3](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.45.2...v2.45.3) (2023-04-06)


### Bug Fixes

* 地图瓦片数据请求时，添加上token，token每10分钟更新一次 ([59edc05](https://git.kicad99.com/bfdx/bf8100-web/commit/59edc05a3540f48ffda5a3e2ce657b933931a6ce))

### [2.45.2](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.45.1...v2.45.2) (2023-03-30)


### Bug Fixes

* 修复其他数据表部分字段更新后，客户端同步异常问题(本地数据被空字段覆盖异常) ([f24f049](https://git.kicad99.com/bfdx/bf8100-web/commit/f24f0495ad0e98a8cd19dd337e5453b29b71640c))
* 修复用户setting字段多客户端同步异常问题 ([ed5ae5d](https://git.kicad99.com/bfdx/bf8100-web/commit/ed5ae5d862b5ef9a0bf529554a120f3b57eae5cd))
* 修复用户数据(setting)部分更新，同步客户端时本地数据被空数据覆盖问题 ([d145364](https://git.kicad99.com/bfdx/bf8100-web/commit/d145364d6993c78a5c34608971e20ad62828b765))

### [2.45.1](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.45.0...v2.45.1) (2023-03-13)


### Bug Fixes

* 修改单位终端列表树节点tooltip显示内容，显示16、10进制的DMRID ([99b824e](https://git.kicad99.com/bfdx/bf8100-web/commit/99b824e49ba754fb80bb4f7c63c19af70856f162))
* 单位、终端、设备、动态组等数据表格DMRID列，显示16和10进制数据 ([d1f5607](https://git.kicad99.com/bfdx/bf8100-web/commit/d1f56077803768d8c044289f7817515dff6cfc1a))

## [2.45.0](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.44.21...v2.45.0) (2023-03-13)


### Features

* Mesh终端类型的终端，不支持动态组功能 ([125972d](https://git.kicad99.com/bfdx/bf8100-web/commit/125972db3405a92314f2ec4960bde17b9c4a9cfc))
* 控制器添加Mesh网关类型 ([9d2ad83](https://git.kicad99.com/bfdx/bf8100-web/commit/9d2ad832addfbdca45d7fec407988a18d00f2ccd))
* 更新协议，添加关于Mesh网关和终端接入协议 ([8ee483b](https://git.kicad99.com/bfdx/bf8100-web/commit/8ee483b8436bdd3703ae8c8a90994941ef652e85))
* 终端管理添加Mesh终端类型 ([c06a041](https://git.kicad99.com/bfdx/bf8100-web/commit/c06a041e96fd314ab6e9a1efd59b6a895d2a310b))
* 终端管理添加Mesh网关终端类型 ([8c31171](https://git.kicad99.com/bfdx/bf8100-web/commit/8c31171f1f63559587af6c95ca3859c5ac62bb66))


### Bug Fixes

* 修复控制器更新时，"sip_no"字段保留旧的dmrId导致新添加的控制器数据失败问题 ([3202d1e](https://git.kicad99.com/bfdx/bf8100-web/commit/3202d1ed73fc9365b733adab318611e6a1c52a1a))
* 发送命令功能窗口，树形列表过滤不支持命令的终端节点，指挥坐席、Mesh终端等部分终端支持短信，其他命令禁用对应的终端节点 ([8425c49](https://git.kicad99.com/bfdx/bf8100-web/commit/8425c49a607515da6d48840d1e6411ddd6e9476d))

### [2.44.21](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.44.20...v2.44.21) (2023-03-03)


### Bug Fixes

* TD510(SDC)R7F、TD510(SVT)添加信道锁定配置 ([a6a520f](https://git.kicad99.com/bfdx/bf8100-web/commit/a6a520f2a544041d136c8744f1aade15e98d11d4))
* 修改手台写频页面设备信息的版本号显示流程，默认显示4位版本号，向下兼容旧版本机型的3位版本号 ([4f9e959](https://git.kicad99.com/bfdx/bf8100-web/commit/4f9e9591f7c54b2741e699a638fe0eaf47e1cce3))

### [2.44.20](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.44.19...v2.44.20) (2023-02-28)


### Bug Fixes

* TD510、TD511、TD930机型的虚拟集群测机信号间隔参数不需要特殊处理，按实际值读、写操作 ([4b89799](https://git.kicad99.com/bfdx/bf8100-web/commit/4b89799f02005154d02509f4d14ef80b6991a762))
* **TD511(SDC)、TD511(SVT):** 写频功能的菜单设置下的"设置"项，开放"漫游界面"选项 ([36110d6](https://git.kicad99.com/bfdx/bf8100-web/commit/36110d68b77c40b275395d31326cc4a431014fde))

### [2.44.19](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.44.18...v2.44.19) (2023-02-27)


### Bug Fixes

* **Sound History:**  修改下载的录音名称名称格式，将时间放到前面，以便用户下载多个录音时排序 ([8c5b712](https://git.kicad99.com/bfdx/bf8100-web/commit/8c5b712def9c75288e4d06ea7b4e9dd6d28da273))
* **TD800(SDC):** R7F芯片机型，显示4段版本号 ([99ccfb6](https://git.kicad99.com/bfdx/bf8100-web/commit/99ccfb60f3d2591bf2b924b31377df5b882b5d24))

### [2.44.18](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.44.17...v2.44.18) (2023-02-17)


### Bug Fixes

* **soundHistory:** 修改下载的录音文件名称，转换时间格式，以提高时间可读性 ([0a3179d](https://git.kicad99.com/bfdx/bf8100-web/commit/0a3179da75839fefa0ff4eb56688f0bc3b94a12f))

### [2.44.17](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.44.16...v2.44.17) (2023-02-13)


### Bug Fixes

* **TD511(SDC)R7F:** 添加信道锁定功能 ([6a40c0f](https://git.kicad99.com/bfdx/bf8100-web/commit/6a40c0f50f6ab6e500323263426be5910b903740))
* **TD511(SVT):** 写频功能添加信道锁定配置 ([0ff1627](https://git.kicad99.com/bfdx/bf8100-web/commit/0ff1627225cfba3a704a80be7ef873532cd1d904))

### [2.44.16](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.44.15...v2.44.16) (2023-02-10)


### Bug Fixes

* **controller:** 中继类型的控制器也添加本地通话视为联网通话选项 ([e9406b2](https://git.kicad99.com/bfdx/bf8100-web/commit/e9406b227eba983693aed74852174104f99c39df))

### [2.44.15](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.44.14...v2.44.15) (2023-02-10)


### Bug Fixes

* **controller:** 同播、集群控制器添加"本地通话视同联网通话(local2net)"选项 ([616cb8c](https://git.kicad99.com/bfdx/bf8100-web/commit/616cb8c380e5c63bab049c6a73c3dfddc9672889))

### [2.44.14](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.44.13...v2.44.14) (2023-01-12)


### Bug Fixes

* node-sass被弃用，使用dart sass替换，支持node v16版本 ([248e33a](https://git.kicad99.com/bfdx/bf8100-web/commit/248e33a957eb4847073074ec828e25b692d140b3))

### [2.44.13](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.44.12...v2.44.13) (2023-01-10)


### Bug Fixes

* 重新编译机型码与名称模块，修复机型码没有正确查找名称异常 ([6d2f2e3](https://git.kicad99.com/bfdx/bf8100-web/commit/6d2f2e303fc94add6c77422956f5463543dcd31e))

### [2.44.12](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.44.11...v2.44.12) (2023-01-10)


### Bug Fixes

* 修复TD511SDC R7F法语定制版本没有正确查找到机型名称错误 ([cbc8840](https://git.kicad99.com/bfdx/bf8100-web/commit/cbc884002ca243c382d76ef89d5512b66aaeb076))

### [2.44.11](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.44.10...v2.44.11) (2023-01-10)


### Bug Fixes

* TD511SDC R7F法语定制版不显示虚拟集群配置 ([78fd3bd](https://git.kicad99.com/bfdx/bf8100-web/commit/78fd3bd8b511d9f72542ea7d066b2708d503e71a))
* 修正TD511 R7F、TD511(FR) R7F机型设备版本显示 ([d76b002](https://git.kicad99.com/bfdx/bf8100-web/commit/d76b00222556a2610bc2add60633d571eaedb1f1))
* 修正TD511(SDC)FR写频组件新建参数时信道重置错误 ([c39c8cb](https://git.kicad99.com/bfdx/bf8100-web/commit/c39c8cbb02eb908fe6a08592d2c4c3f14fdc2bd5))
* 修正TD511SDC信道不显示SVT站点设置 ([47bd214](https://git.kicad99.com/bfdx/bf8100-web/commit/47bd214889b07237a837c35f2a9272a165433ea1))
* **Channel:** 信道版本9的配置组件，在开启了SVT配置时才显示"SVT站点信息"参数配置 ([9b8b3d8](https://git.kicad99.com/bfdx/bf8100-web/commit/9b8b3d855b90e9b057d20dcacd794b4c2f783231))
* **TD511(SDC)FR:** 修正、兼容法语定制机型R7F芯片版本信道、虚拟集群等协议配置 ([2a5c4fb](https://git.kicad99.com/bfdx/bf8100-web/commit/2a5c4fb6e6959fc9f6d7ebac4e2a568afb39d7ae))

### [2.44.10](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.44.9...v2.44.10) (2023-01-10)


### Bug Fixes

* **TD511(SDC):** R7F芯片版本，信道协议变更为10版本，并添加对应的虚拟集群和站点信息配置 ([6a899cd](https://git.kicad99.com/bfdx/bf8100-web/commit/6a899cd3a50cdb2d0d7fc588ff5716bda357d782))

### [2.44.9](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.44.8...v2.44.9) (2023-01-04)


### Bug Fixes

* **CI:** 更新deploy-web和pkg-push作业rules规则，更新README.md中的"Enable_License"变更说明 ([558bf6b](https://git.kicad99.com/bfdx/bf8100-web/commit/558bf6b08129948aa9871a6003bf37509e928d24))

### [2.44.8](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.44.7...v2.44.8) (2023-01-04)


### Bug Fixes

* **CI:** 修改lint作业在合并请求时触发，修改deploy-web作业需要手动触发部署 ([b2e7568](https://git.kicad99.com/bfdx/bf8100-web/commit/b2e756832fc0464be1ba6679d12b03928bc180b5))

### [2.44.7](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.44.6...v2.44.7) (2023-01-04)


### Bug Fixes

* **License:** 添加常规终端接入授权选项翻译 ([35177c6](https://git.kicad99.com/bfdx/bf8100-web/commit/35177c6a22f6d5f81487402f733badf71615bcf8))

### [2.44.6](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.44.5...v2.44.6) (2023-01-04)


### Bug Fixes

* **License:** 添加是否开启授权校验环境变量配置 ([bdac9d5](https://git.kicad99.com/bfdx/bf8100-web/commit/bdac9d562b85f5adb0bd2460daa0423ec1f2ddb6))
* **License:** 读取GitLab CI的变量，生成是否开启授权校验环境配置 ([49edf78](https://git.kicad99.com/bfdx/bf8100-web/commit/49edf784683c2f910a0033fe88aa0e3acb95d73f))
* 更新caniuse-lite浏览器兼容依赖 ([fa7dd35](https://git.kicad99.com/bfdx/bf8100-web/commit/fa7dd35c835a4ed0b11a014e9107000b227c8c54))
* **TD511(SDC)FR:** 新增'511FRR00'机型码匹配 ([20057e9](https://git.kicad99.com/bfdx/bf8100-web/commit/20057e98af830c5e2c91a719b33507c28f7fee86))

### [2.44.5](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.44.4...v2.44.5) (2022-11-22)


### Bug Fixes

* **Login:** 修改登录背景图加载方式，使用siteConfig配置来加载指定的背景图 ([699e72c](https://git.kicad99.com/bfdx/bf8100-web/commit/699e72cba53931caf7173b4984bc76948370b7b5))
* **Logo:** 修改加载logo图片方法，使用siteConfig配置来加载指定后缀的logo ([9c1d34c](https://git.kicad99.com/bfdx/bf8100-web/commit/9c1d34cdc0abd8353c9ea68e1cc514146b72e91a))

### [2.44.4](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.44.3...v2.44.4) (2022-11-11)


### Bug Fixes

* **对讲机写频:** 修复TD510(SDC)和TD511(SDC)机型R7F芯片版本接收组和信道数据编码时的数据结构长度不正确错误 ([e91fdb3](https://git.kicad99.com/bfdx/bf8100-web/commit/e91fdb39c8cfa30c86fd50b3c93c84e1bdfea654))

### [2.44.3](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.44.2...v2.44.3) (2022-11-08)


### Bug Fixes

* **controller:** 添加监听"Ctrl+Alt+P"快捷键打开ping配置项，快捷键在更新页下生效 ([5265ec6](https://git.kicad99.com/bfdx/bf8100-web/commit/5265ec603825fc6671abd9695d7d9adb27845070))

### [2.44.2](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.44.1...v2.44.2) (2022-10-18)


### Bug Fixes

* **datatables:**  在调用列的adjust方法后，重新调用表格draw方法，以解决数据更新后跳转数据页后可能出现空白区域问题 ([36c726c](https://git.kicad99.com/bfdx/bf8100-web/commit/36c726c8c5d7701cfd07fa825ec73be5888faa59))
* **datatables:** 更新datatables模块版本 ([1bceef1](https://git.kicad99.com/bfdx/bf8100-web/commit/1bceef1026e902fa11d622157137e45cb9e15bcc))

### [2.44.1](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.44.0...v2.44.1) (2022-10-14)


### Bug Fixes

* **repeater:** TR8500M、TR900M 、TR925M系列的所有功率相关都改为低（0）、中（1）、高（2）、自定义(3) 四个档次 ([39bc476](https://git.kicad99.com/bfdx/bf8100-web/commit/39bc47660a1ceb9b5eaa091373d28c29a351212d))

## [2.44.0](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.43.8...v2.44.0) (2022-10-14)


### Features

* **map:** 地图添加天地图图层支持，并开放配置 ([a2bc909](https://git.kicad99.com/bfdx/bf8100-web/commit/a2bc90922f416e47b73eaf1c640efca1d2bd365c))


### Bug Fixes

* **web:** 将所有的jsons配置数据，转移到public/config.js中 ([51a348a](https://git.kicad99.com/bfdx/bf8100-web/commit/51a348a82ab00918f4f2de2b1f8aa5f99a34566d))

### [2.43.8](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.43.7...v2.43.8) (2022-10-13)


### Bug Fixes

* **TR900M:** 修复中继信道名称为空时，对应的参数为数组时导致的解码失败问题 ([279efea](https://git.kicad99.com/bfdx/bf8100-web/commit/279efea871a878ab445ea133ac55de5c79a468e8))

### [2.43.7](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.43.6...v2.43.7) (2022-10-08)


### Bug Fixes

* **TR900M:** 兼容R7F芯片RR900M中继 ([1f0a71a](https://git.kicad99.com/bfdx/bf8100-web/commit/1f0a71a0de6f543be1d31fa9145399c78b2df5ff))

### [2.43.6](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.43.5...v2.43.6) (2022-09-22)


### Bug Fixes

* **DZ148SVT:** 修改虚拟中继温度状态，温度过低也显示正常 ([8a9c5c8](https://git.kicad99.com/bfdx/bf8100-web/commit/8a9c5c8ac7ada340d299c4c41633fe69295d6b73))

### [2.43.5](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.43.4...v2.43.5) (2022-09-19)


### Bug Fixes

* **TD510(SVT):** 该机型为无屏机，屏蔽与界面有关的配置参数 ([f0773c1](https://git.kicad99.com/bfdx/bf8100-web/commit/f0773c1c0a0794fb681a430d2f519c62a34a5ef0))
* TD510SVT, TD511SVT为R7F芯片机型，同样显示4段版本号 ([d2d75d9](https://git.kicad99.com/bfdx/bf8100-web/commit/d2d75d9f07c8f444aeeac5ccd06d921d5f31aac9))

### [2.43.4](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.43.3...v2.43.4) (2022-09-15)


### Bug Fixes

* **TD510(SVT):** 格式化站点信息配置 ([643fcd0](https://git.kicad99.com/bfdx/bf8100-web/commit/643fcd09191407f9016646865c1b6dedbbab952a))

### [2.43.3](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.43.2...v2.43.3) (2022-09-15)


### Bug Fixes

* **TD510(SVT):** 修复虚拟集群站点编解码类没有找到错误 ([325ce25](https://git.kicad99.com/bfdx/bf8100-web/commit/325ce259cad0b8d3cc4397ae99ccf7abddd59188))

### [2.43.2](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.43.1...v2.43.2) (2022-09-15)


### Bug Fixes

* **TD510(SVT):** 修复虚拟集群编码类this关键词错误 ([c1e953a](https://git.kicad99.com/bfdx/bf8100-web/commit/c1e953ad40ecfc096bf24b80ae2789e0dda739a7))

### [2.43.1](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.43.0...v2.43.1) (2022-09-14)


### Bug Fixes

* TD511(SVT)、TD510(SVT0)站点信息的写入限制为一个站点，避免超出320字节 ([741d434](https://git.kicad99.com/bfdx/bf8100-web/commit/741d434d98ae5108f765ac99b76215484f6f9724))

## [2.43.0](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.42.1...v2.43.0) (2022-09-09)


### Features

* **TD511(SVT):** 信道版本变更为10, 添加站点信息配置 ([24906b8](https://git.kicad99.com/bfdx/bf8100-web/commit/24906b877b4a31a346ef2e54ada607d232b82ee1))
* **TD511(SVT):** 添加站点信息配置 ([7f376bc](https://git.kicad99.com/bfdx/bf8100-web/commit/7f376bc42a03c84e914ecb8ac1dea9ad22bc8cf9))
* **TD511(SVT):** 虚拟集群版本变更为1 ([5462374](https://git.kicad99.com/bfdx/bf8100-web/commit/546237495d49ed8d5d495ec643035e6a9683843f))


### Bug Fixes

* **TD511(SVT):** 写入数据时，找到读取数据时缓存的结构信息配置，默认以缓存的结构配置进行数据编码 ([ed3e057](https://git.kicad99.com/bfdx/bf8100-web/commit/ed3e0576781cceb0f04b16893a2f58f98b6e8025))
* **TD930(SVT):** 修改信道频率校验规则 ([0ebfa42](https://git.kicad99.com/bfdx/bf8100-web/commit/0ebfa42d15b707d92e0270d71b6bcdbab61417c9))

### [2.42.1](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.42.0...v2.42.1) (2022-09-08)


### Bug Fixes

* **ci:** update yarn config env ([d6a0845](https://git.kicad99.com/bfdx/bf8100-web/commit/d6a084563c59d843b9f4bd367667d0ad821520f2))
* **TD930(SVT):** 修复ext-fancytree节点名称太长时,没有横向滚动条问题 ([8d595af](https://git.kicad99.com/bfdx/bf8100-web/commit/8d595afeec72344cea561258f1c501b3be235bad))
* **TD930(SVT):** 修复写入失败时，写入按钮状态没有重置异常 ([6bf8491](https://git.kicad99.com/bfdx/bf8100-web/commit/6bf8491bf1aa5d0ed47f6b6804dc45051423b638))
* **TD930(SVT):** 修复写入虚拟集群站点信息前没有先清除旧数据异常(列表类数据需要先清除旧数据) ([1da3046](https://git.kicad99.com/bfdx/bf8100-web/commit/1da30468105536f7bf5cff79268a3c7ffd5793fd))

## [2.42.0](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.41.9...v2.42.0) (2022-08-23)


### Features

* **TD930(SVT):** 关联数字信道与虚拟集群的站点信息功能 ([439ce33](https://git.kicad99.com/bfdx/bf8100-web/commit/439ce334c9a8c20ff0ed52be34c3eb79541e42f0))
* **TD930(SVT):** 机型虚拟集群添加测机信号间隔参数配置 ([7a651e7](https://git.kicad99.com/bfdx/bf8100-web/commit/7a651e730c2166aee618afbd53144d758cdbf4b2))
* **TD930(SVT):** 添加站点信息导航菜单及右键菜单配置 ([90adff3](https://git.kicad99.com/bfdx/bf8100-web/commit/90adff3201dc1b53324b3199ae2cdf464a22ca16))
* **TD930(SVT):** 添加站点信息配置组件,实现相关参数配置 ([7cdcf38](https://git.kicad99.com/bfdx/bf8100-web/commit/7cdcf38c3c5d0cc09d3cb1cef1ab2d37fdf0151a))
* **TD930(SVT):** 站点信息频率参数添加动态表单校验规则和同步表格选中行数据 ([98a6490](https://git.kicad99.com/bfdx/bf8100-web/commit/98a6490d46d6444332fc8d311064d17fb5c66fde))
* update TD930(SVT) protocol xlsx ([74e7a99](https://git.kicad99.com/bfdx/bf8100-web/commit/74e7a99cb75cc23b2c304edd0bf66cd4a718b3dd))

### [2.41.9](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.41.8...v2.41.9) (2022-08-02)


### Bug Fixes

* 设备管理的“同播中继”和“虚拟集群中继”类型的中继，“所属单位”参数强制与上级控制器一致 ([5b6d96f](https://git.kicad99.com/bfdx/bf8100-web/commit/5b6d96f5ed03ac32d92a7d079e89b75a01df9acd))
* 设备管理的“联网时隙”改为“联网信道” ([a9ca050](https://git.kicad99.com/bfdx/bf8100-web/commit/a9ca050898a361da6384b7dc89c22faf487866d9))

### [2.41.8](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.41.7...v2.41.8) (2022-07-29)


### Bug Fixes

* 没有日志数据时禁用清除和导出按钮,及关闭窗口时清空日志 ([2234d39](https://git.kicad99.com/bfdx/bf8100-web/commit/2234d39e63d9d65d39161855b010bf11c2d1542f))

### [2.41.7](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.41.6...v2.41.7) (2022-07-28)


### Bug Fixes

* 修改原系统日志,使用虚拟滚动显示日志消息 ([f1f0646](https://git.kicad99.com/bfdx/bf8100-web/commit/f1f06464c7cca0197d675e487580e7e20267520b))
* 修改原系统日志,使用虚拟滚动显示日志消息,2秒内连续点击运行日志标题5次打开 ([aa98df5](https://git.kicad99.com/bfdx/bf8100-web/commit/aa98df5b4119f8164a3c42e0ae539a38cb222d9a))

### [2.41.6](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.41.5...v2.41.6) (2022-07-27)


### Bug Fixes

* 修改同步服务器时间逻辑,改在初次连接服务器后进行 ([c51151c](https://git.kicad99.com/bfdx/bf8100-web/commit/c51151cbf9d2d7d6824ab88b4dfc502c3c5a8e39))
* 协议到期时间检验使用服务器和客户端时间的最大值 ([a73032a](https://git.kicad99.com/bfdx/bf8100-web/commit/a73032a5f7aa83183eaad2038ea4d4a15d537b73))

### [2.41.5](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.41.4...v2.41.5) (2022-07-20)


### Bug Fixes

* update browserslist@latest ([569e611](https://git.kicad99.com/bfdx/bf8100-web/commit/569e611ac7d46fcaac503b6cde833931a7d722ed))
* 修复对讲机写频设备信息的4段版本号在切换功能页后显示异常 ([f154681](https://git.kicad99.com/bfdx/bf8100-web/commit/f1546813332982eda640c04321f159b28d597718))
* 修改910系列机型写频菜单设置禁用关系，保持与写频软件同步 ([a3c2183](https://git.kicad99.com/bfdx/bf8100-web/commit/a3c2183fe163c0a542c9580a5944218bd2efc029))
* 修改卫星定位配置的查询命令输入限制,不允许输入中文 ([3935d63](https://git.kicad99.com/bfdx/bf8100-web/commit/3935d63df3953b023e96fd87ae12b070d576600f))
* 对讲机写频的按键定义不开放生产调试选项 ([c3eaf67](https://git.kicad99.com/bfdx/bf8100-web/commit/c3eaf67001aea97b2513a973d8bc65e78a183962))

### [2.41.4](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.41.3...v2.41.4) (2022-07-19)


### Bug Fixes

*  910和910P菜单设置屏蔽"背光"选项 ([d9d8097](https://git.kicad99.com/bfdx/bf8100-web/commit/d9d8097c14df16cda3792d44fbe93d3cb0bc34bf))

### [2.41.3](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.41.2...v2.41.3) (2022-07-18)


### Bug Fixes

* BF-TD910P(SDC)芯片R7F版本显示4段版本号 ([c6d1be0](https://git.kicad99.com/bfdx/bf8100-web/commit/c6d1be0fece7074a948c1c7298b96c1a1f8963ff))
* 修改授权到期提示，显示剩余天数即可 ([d044302](https://git.kicad99.com/bfdx/bf8100-web/commit/d0443026c956f12ff7b719c1ee416fafbca6e671))

### [2.41.2](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.41.1...v2.41.2) (2022-07-15)


### Bug Fixes

* 修复中继写频兼容R7F芯片型号导致候选中继重复加载错误 ([24760da](https://git.kicad99.com/bfdx/bf8100-web/commit/24760dab54e35f1a3963a4eef6e5e187aa11616c))
* 修复动态组更新时，没有找到目标动态组也提示更新成功错误 ([2af769a](https://git.kicad99.com/bfdx/bf8100-web/commit/2af769a01c0201b6c862d9988a823bd2e9507973))

### [2.41.1](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.41.0...v2.41.1) (2022-07-14)


### Bug Fixes

* 授权信息取消modal遮罩，解决遮罩层的zIndex被覆盖显示错乱 ([d8c799a](https://git.kicad99.com/bfdx/bf8100-web/commit/d8c799a228dc51047453a527f0a4e90089f20b8d))

## [2.41.0](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.40.0...v2.41.0) (2022-07-14)


### Features

* TD910,TD910P新加芯片R7F机型码 ([5a3644c](https://git.kicad99.com/bfdx/bf8100-web/commit/5a3644c1263ff317a1c04c0298ff25f4e50b6043))
* 机型检测方法兼容TD910SDC(SDC)和TD910P(SDC)新加的R7F芯片机型码 ([29997f1](https://git.kicad99.com/bfdx/bf8100-web/commit/29997f13e59acd89ef2ad3d9f500b00da11cfc18))

## [2.40.0](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.39.0...v2.40.0) (2022-07-14)


### Features

* BF-TR8500M兼容BF-TR8500M(R7F)机型 ([03c85c2](https://git.kicad99.com/bfdx/bf8100-web/commit/03c85c2db30e76d997f29385be658683878f0326))
* 添加BF-TR8500M(R7F)机型码 ([7b13d90](https://git.kicad99.com/bfdx/bf8100-web/commit/7b13d904ecee14b9ad88cb280bb6d4fd93dd7089))

## [2.39.0](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.38.15...v2.39.0) (2022-07-13)


### Features

* 在帮助菜单下，添加"授权信息"菜单选项 ([e69708c](https://git.kicad99.com/bfdx/bf8100-web/commit/e69708c28a908ceac6a7f31e7a579847dea6bcab))
* 完成授权功能的导入及申请 ([d0be991](https://git.kicad99.com/bfdx/bf8100-web/commit/d0be9910bfbe8d5770dd031fe2375093b53542dc))
* 打开菜单检测到没有授权时，按授权模块进行消息提示 ([a91dbd4](https://git.kicad99.com/bfdx/bf8100-web/commit/a91dbd4be6f7adf33505ebe5c562c3e4f786f3ee))
* 授权到期前30天内进行警告框提示授权续期 ([9945966](https://git.kicad99.com/bfdx/bf8100-web/commit/994596683e05407da21ab04df17b17a26314bbc8))
* 添加上传导入授权文件协议及接口 ([5993774](https://git.kicad99.com/bfdx/bf8100-web/commit/5993774de36b3733cb6f873390caec416e64ec4e))
* 添加导航菜单索引与授权模块映射关系 ([353c0b6](https://git.kicad99.com/bfdx/bf8100-web/commit/353c0b681e2d38c88fb1e3e3dd4daedb55ebf968))
* 添加授权信息及申请页面 ([32b9954](https://git.kicad99.com/bfdx/bf8100-web/commit/32b9954a5550e31d5293f1c125208d221f80735b))
* 添加授权协议 ([433c55c](https://git.kicad99.com/bfdx/bf8100-web/commit/433c55c5ddc9143d07479c94c5b08219c7d8081e))
* 登录后，检测授权信息，如果没有授权，则警告提示且不再执行后续请求数据逻辑 ([777b1d7](https://git.kicad99.com/bfdx/bf8100-web/commit/777b1d765c5c3bb3f67efaad910d49e8d2d77808))


### Bug Fixes

* 修改读取当前UTC时间方法,使用"dayjs().utc()" ([cc3683b](https://git.kicad99.com/bfdx/bf8100-web/commit/cc3683b6bcab5713d49982bebf6108527d800d67))

### [2.38.15](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.38.14...v2.38.15) (2022-06-21)


### Bug Fixes

* SIP网关配置在后台运行时才需要表单检验 ([2e464db](https://git.kicad99.com/bfdx/bf8100-web/commit/2e464db0c33abd5991f379cc99775f50ae97cca7))

### [2.38.14](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.38.13...v2.38.14) (2022-06-07)


### Bug Fixes

* 删除漫游组表单无效的参数 ([81e281d](https://git.kicad99.com/bfdx/bf8100-web/commit/81e281d7de4f0576167ab210eeea301aa63b5228))
* 调整TD930SVT写频树形菜单点击事件逻辑，将部分常用的配置使用v-show指令加载 ([32d3fc2](https://git.kicad99.com/bfdx/bf8100-web/commit/32d3fc22a885620e4aa0ca71722ada85eb0ad743))

### [2.38.13](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.38.12...v2.38.13) (2022-06-06)


### Bug Fixes

* 禁用TD510SDC的R7F芯片调试判断 ([765f29f](https://git.kicad99.com/bfdx/bf8100-web/commit/765f29f9cafc271bafe8d16786263d76a3065e1c))

### [2.38.12](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.38.11...v2.38.12) (2022-06-06)


### Bug Fixes

* 修复TD510、TD511系列机型在读取数数据信道配置同步时没有同步当前信道设置页面的参数导致写入数据异常错误 ([4451cac](https://git.kicad99.com/bfdx/bf8100-web/commit/4451cac5827608102bc5a6773bfd11b617fbcac1))
* 修复虚拟集群组件没有正确调用表单校验错误 ([b3e7e82](https://git.kicad99.com/bfdx/bf8100-web/commit/b3e7e82e38cf6d41b0e52578f203ef5c896f63ff))
* 修改TD510、TD511系列机型9版本信道验证过程 ([6632fe4](https://git.kicad99.com/bfdx/bf8100-web/commit/6632fe4b746b38ae187616d74eb60231acc486ca))

### [2.38.11](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.38.10...v2.38.11) (2022-06-02)


### Bug Fixes

* TD511SVT机型使用9版本信道组件 ([8f06145](https://git.kicad99.com/bfdx/bf8100-web/commit/8f06145a195349742c7923483c5078893f8eb39e))
* TD511系列机型使用虚拟集群公共组件 ([e2f55b9](https://git.kicad99.com/bfdx/bf8100-web/commit/e2f55b910ae272f23d1609a61d4824048b4d7064))
* TD511系列机型写频，在读取数据结束后，如果有选择终端，则重置终端数据 ([ba4c381](https://git.kicad99.com/bfdx/bf8100-web/commit/ba4c3818ff7319df8da09d5c52642b36b1f74264))
* 修复9版本信道参数高功率默认值错误 ([be1aa6f](https://git.kicad99.com/bfdx/bf8100-web/commit/be1aa6f15d1c43d4811c5ec99fc23248656f4735))
* 修复TD511SVT机型加密功能基础密钥不支持小写字母错误 ([9752429](https://git.kicad99.com/bfdx/bf8100-web/commit/9752429288576474cfa89f50d79e130528c32047))
* 修复TD930SVT虚拟集群信道列表被空数据占用和svt信道设置标题不匹配问题 ([06f19ec](https://git.kicad99.com/bfdx/bf8100-web/commit/06f19ec33201265cb4929f0962a47eecb53dc21f))
* 修复新建写频数据，通讯录重载时缓存处理错误 ([ac4b2aa](https://git.kicad99.com/bfdx/bf8100-web/commit/ac4b2aa1b8b271b696b45376bfc7d911bd951d5c))
* 提取频点映射逻辑为独立组件，统一使用 ([ca7eda8](https://git.kicad99.com/bfdx/bf8100-web/commit/ca7eda8247ae84323bf2fbc36368a487e6cf10c9))

### [2.38.10](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.38.9...v2.38.10) (2022-05-31)


### Bug Fixes

* 930SVT虚拟集群归属组，从联系人中选择 ([5a860c1](https://git.kicad99.com/bfdx/bf8100-web/commit/5a860c13e363a6f7d46d16a8fa61611569b04c05))
* TD510、TD511系列机型加密配置各密钥列表开放密钥ID编辑功能 ([0f4dfd6](https://git.kicad99.com/bfdx/bf8100-web/commit/0f4dfd6dcda96887530968d8d81aa2ce183451ac))
* 修复高级加密AES数据包过大导致无法写入错误(3个密钥一个数据包，超出上限) ([032341a](https://git.kicad99.com/bfdx/bf8100-web/commit/032341a458bec9ec22b30d9dc69c1a7bf6353c6b))

### [2.38.9](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.38.8...v2.38.9) (2022-05-30)


### Bug Fixes

* 511FR000机型使用9版本信道组件 ([818fbc0](https://git.kicad99.com/bfdx/bf8100-web/commit/818fbc0fd241e28934caef6e174e2c66edc7fe71))
* 修复9版本信道组件一些参数读取错误 ([c2eb0c7](https://git.kicad99.com/bfdx/bf8100-web/commit/c2eb0c740dbb1176e7026137d41a3b6f83a4df69))
* 修复虚拟集群归属组显示异常问题 ([ea22420](https://git.kicad99.com/bfdx/bf8100-web/commit/ea224200e7c57c8e183aa2bc7bf46d6400e8194e))
* 修改"高级加密ARS"为"高级加密AES" ([dc1e210](https://git.kicad99.com/bfdx/bf8100-web/commit/dc1e2102920798a12344149d3eb08f4fe2427875))
* 修改510SVT00使用公共的加密组件和9版本信道组件 ([7a49696](https://git.kicad99.com/bfdx/bf8100-web/commit/7a49696a5a4cce47a4890982d4b5fb0291bbfdd4))
* 修改511FR000使用公共的加密组件 ([db7fab1](https://git.kicad99.com/bfdx/bf8100-web/commit/db7fab192185e14837762119cc904de2b3f423ff))
* 修改9版本信道密钥列表下拉列表参数值为密钥序号 ([4065800](https://git.kicad99.com/bfdx/bf8100-web/commit/40658008aa23c4ab9f87c0052e8e373b6737610c))
* 对讲机写频功能信道参数的扫描列表ID参数如果无效，则下拉列表参数值变更为0xFF ([c11b706](https://git.kicad99.com/bfdx/bf8100-web/commit/c11b706d6b1d6305ea4f154a0b3879124454f830))
* 对讲机写频功能限制按键定义短信下拉列表宽度 ([064230d](https://git.kicad99.com/bfdx/bf8100-web/commit/064230dc293a400335799ab3308b82cbf90488ef))

### [2.38.8](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.38.7...v2.38.8) (2022-05-27)


### Bug Fixes

* TD930SVT虚拟集群归属组在读取数据后显示空白问题 ([a58a552](https://git.kicad99.com/bfdx/bf8100-web/commit/a58a5528229ae7b504715dc42ba1b9502c5ac26b))
* 修复信道9版本组件没有同步当前信道参数问题 ([986cb37](https://git.kicad99.com/bfdx/bf8100-web/commit/986cb3711231c3a7ab47805d7bc946d583065a5b))

### [2.38.7](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.38.6...v2.38.7) (2022-05-27)


### Bug Fixes

* 信道9版本尾音选择和爆破音在数字亚音下禁用选择 ([65fca1c](https://git.kicad99.com/bfdx/bf8100-web/commit/65fca1c89aca8221a2fdd16d352c3b65bd3aa343))
* 修复TD510、TD511写频功能同步信道参数逻辑 ([359a87b](https://git.kicad99.com/bfdx/bf8100-web/commit/359a87b0e69e5544e456a93982e6f24490e1afc3))
* 修复信道9版本的接收组列表参数"无"参数默认值为0xFFFF ([b99767b](https://git.kicad99.com/bfdx/bf8100-web/commit/b99767b5ad092ef00bca5ab70e8c6e7d8169421e))
* 修复字符串版本号大小比较错误 ([3ee53ba](https://git.kicad99.com/bfdx/bf8100-web/commit/3ee53ba565db97548acdf0ef57246eefb85415b3))
* 基础密钥版本0允许输入小写字母和"!@#$%^&*()"字符 ([e1866bb](https://git.kicad99.com/bfdx/bf8100-web/commit/e1866bb3af7f707e12bb5fd5fe4d5c28a1daf46e))
* 对讲机写频按键定义，短信内容下拉列表限制最大宽度 ([611267e](https://git.kicad99.com/bfdx/bf8100-web/commit/611267e82842eb7433a0b71ca716570873abe788))

### [2.38.6](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.38.5...v2.38.6) (2022-05-25)


### Bug Fixes

* 修复TD510、TD511机型SDC系列信道9版本显示"SVT联网模式"问题 ([8bf5d16](https://git.kicad99.com/bfdx/bf8100-web/commit/8bf5d16e2445ea7e9436902e498c6699a4c67cba))
* 修复TD510、TD511系列常规设置的时区参数错误 ([685d9d0](https://git.kicad99.com/bfdx/bf8100-web/commit/685d9d09487aa163f38553bb7a0d674b238815d2))
* 修复TD510(SDC)、TD511(SDC)R7F芯片机型信道协议版本变更导致信道功率参数显示错乱错误 ([8ffb699](https://git.kicad99.com/bfdx/bf8100-web/commit/8ffb69912cd2c6331e4faec309a48950adb323a4))
* 提取时区参数配置组件，年、月、日使用的是本机实时时间 ([efe9110](https://git.kicad99.com/bfdx/bf8100-web/commit/efe911083dbbee2184ebb2f9eff4af70a8048587))

### [2.38.5](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.38.4...v2.38.5) (2022-05-24)


### Bug Fixes

* 修复TD510(SDC)、TD511(SDC)R7F芯片机型再次读取数据时没有清除旧的加密密钥列表问题 ([f14ce0a](https://git.kicad99.com/bfdx/bf8100-web/commit/f14ce0a126ec214729a515281ef8b88130733d5a))

### [2.38.4](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.38.3...v2.38.4) (2022-05-24)


### Bug Fixes

* TD510(SDC)、TD511(SDC)接收组添加1版本的协议，以适配置R7F芯片机型 ([3251940](https://git.kicad99.com/bfdx/bf8100-web/commit/325194005fa5d629d42d4b22af0bfaaba126fa33))
* 修复TD510(SDC)、TD511(SDC)R7F芯片机型新建数据时，没有清除加密配置导致数据重置错误 ([38b19bd](https://git.kicad99.com/bfdx/bf8100-web/commit/38b19bd758229e18d40a5af38df4eee4472a4189))
* 修复TD510(SDC)、TD511(SDC)R7F芯片机型读取数据时协议长度参数错误导致无法读取问题 ([6b72475](https://git.kicad99.com/bfdx/bf8100-web/commit/6b72475198923100c7226d52cb96a1780f1ffe25))

### [2.38.3](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.38.2...v2.38.3) (2022-05-24)


### Bug Fixes

* release some fix ([bc991d7](https://git.kicad99.com/bfdx/bf8100-web/commit/bc991d7d1606afd95f92461b6a1eeb6472a6ea19))

### [2.38.2](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.38.1...v2.38.2) (2022-05-23)


### Bug Fixes

* 修复TD510(SVT)、TD511(SVT)机型无法读取问题，信道的虚拟集群只有数字信道才有 ([b0e45c3](https://git.kicad99.com/bfdx/bf8100-web/commit/b0e45c385566b168526b024dc0cee189cb75ebcf))
* 修复TD930(SVT)机型，selectDeviceData计算属性访问devGroup参数错误 ([8778cb0](https://git.kicad99.com/bfdx/bf8100-web/commit/8778cb0b56eba44722391758d4b01cbb25daef52))
* 修复虚拟集群参数结构长度错误导致无法写入数据问题 ([ce142df](https://git.kicad99.com/bfdx/bf8100-web/commit/ce142dfc1d08f30389ef9e2ffee792f4c0acff0e))
* 写频功能的信道设置各类型参数赋值前先判断是否存在该配置对象 ([05d9298](https://git.kicad99.com/bfdx/bf8100-web/commit/05d9298af9b18f7aecc13fe62d6012eb96b898f9))

### [2.38.1](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.38.0...v2.38.1) (2022-05-19)


### Bug Fixes

* 修复发送命令或能中包含经度、纬度坐标的命令没有校验坐标有效范围错误 ([3f6325a](https://git.kicad99.com/bfdx/bf8100-web/commit/3f6325aea1f5fb4a4e3dad29558882227b3a470d))

## [2.38.0](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.37.0...v2.38.0) (2022-05-18)


### Features

* 以BF-TD511(SDC)/BF-TD511(SVT)为模板，添加BF-TD880(SDC)/BF-TD880(SVT)机型写频功能，只是机型码不一新 ([6670a0a](https://git.kicad99.com/bfdx/bf8100-web/commit/6670a0afd9c7cadb90c63a12b709bd6130d0ef6c))
* 添加BF-TD880(SDC)/BF-TD880(SVT)机型信道区域配置 ([e0a0be7](https://git.kicad99.com/bfdx/bf8100-web/commit/e0a0be71c7e856159a2cf8989a5ff783fab0ec8a))


### Bug Fixes

* 更新进入系统的机型配置说明 ([6cf06be](https://git.kicad99.com/bfdx/bf8100-web/commit/6cf06be8a650a26c4cd69462b37a22f194fbabcb))

## [2.37.0](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.36.3...v2.37.0) (2022-05-17)


### Features

* TD510(SDC)、TD511(SDC)机型兼容的R7F机型写频功能添加加密功能协议和9版本的信道协议 ([2313d95](https://git.kicad99.com/bfdx/bf8100-web/commit/2313d95e729390db70ed3cd08d2a1a2d67830ab3))
* TD510(SDC)、TD511(SDC)机型兼容的R7F机型写频功能的常规设置隐藏"语音加密类型"和"语音加密密钥" ([471eeb0](https://git.kicad99.com/bfdx/bf8100-web/commit/471eeb0cb37385ee6b098be25a660bef8586f461))
* TD510(SDC)、TD511(SDC)机型兼容的R7F机型写频功能需要把读取上来的虚拟集群参数写到R7F机型的机子中，否则可能会报错 ([9c4f3ea](https://git.kicad99.com/bfdx/bf8100-web/commit/9c4f3ea7d205087b8bffce15f5a7e48523d1ad05))
* TD510(SDC)、TD511(SDC)机型写频功能按键定义添加"加密开关"选项 ([ec64234](https://git.kicad99.com/bfdx/bf8100-web/commit/ec64234df4888f7a64c839a5a49c4f2c19f80f0d))
* TD510(SDC)、TD511(SDC)机型写频功能添加兼容R7F主控芯片的机型码 ([fe4c301](https://git.kicad99.com/bfdx/bf8100-web/commit/fe4c301c382946c1453553958c5f2188695aef99))
* TD510(SDC)兼容R7F芯片的信道9版本的数据 ([e5c9262](https://git.kicad99.com/bfdx/bf8100-web/commit/e5c9262578114f9818127c78a01e39605b59e963))
* TD511(SDC)兼容R7F芯片的信道9版本的数据 ([7e3e045](https://git.kicad99.com/bfdx/bf8100-web/commit/7e3e0450a2d716590724c2cff133582e3da46ef6))
* 以TD511(SVT)为模板，添加TD510(SVT)机型写频功能，删除掉"菜单定义"和"电话本"功能 ([921afbe](https://git.kicad99.com/bfdx/bf8100-web/commit/921afbe1260470c7ef7ccd2a82c18d91d447d122))
* 兼容TD510、TD511的R7F芯片机型写频加密配置和密钥列表功能 ([63e4c00](https://git.kicad99.com/bfdx/bf8100-web/commit/63e4c006a717b0138b0ead550117f908dcb2bcd0))
* 完成提取TD510、TD511机型写频加密列表组件 ([b7bcf7f](https://git.kicad99.com/bfdx/bf8100-web/commit/b7bcf7ff3a7c7b5bbfdd51eedca6647755a7cc62))
* 提取TD510、TD511机型写频加密列表组件 ([84ea2b0](https://git.kicad99.com/bfdx/bf8100-web/commit/84ea2b0d71d32ab2b6c120c95040beb2d20d9a69))
* 提取信道协议版本为9的信道组件 ([4b3239b](https://git.kicad99.com/bfdx/bf8100-web/commit/4b3239bdd0d44c1831c87a3e4534cf5b60f0767c))


### Bug Fixes

* TD511(SVT)按键定义新增"加密开关"选项 ([2b420bc](https://git.kicad99.com/bfdx/bf8100-web/commit/2b420bcde1a862e6dc20401925b1b21545c44f59))
* 修复TD510(SDC)、TD511(SDC)写频功能判断是否为R7F芯片的计算属性错误 ([4434262](https://git.kicad99.com/bfdx/bf8100-web/commit/4434262be396afb07bae5644094656203dd8f1d4))
* 修复版本9的信道加密配置禁用规则没有配置加密功能问题 ([465ed29](https://git.kicad99.com/bfdx/bf8100-web/commit/465ed29ed6b497af9ab89ba15262a606c3ffffa7))

### [2.36.3](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.36.2...v2.36.3) (2022-05-12)


### Bug Fixes

* 修改对讲机打卡时坐标偏移距离(5到10米)和方向范围(-120到120度) ([7592c18](https://git.kicad99.com/bfdx/bf8100-web/commit/7592c1857860b862696d83abae7642bb128be36a))
* 修改对讲机的地图marker名称位置，显示在图标顶部，避免打卡时与巡查点名称重叠 ([ce73791](https://git.kicad99.com/bfdx/bf8100-web/commit/ce73791d65bd10ab56cbd1ab9aa5c48b9bbef165))
* 对讲机打卡时，以卡点坐标进行随机偏移，避免重叠 ([b09336d](https://git.kicad99.com/bfdx/bf8100-web/commit/b09336d613cd24edc0b27f86aad0ba3ddf5d01ab))

### [2.36.2](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.36.1...v2.36.2) (2022-05-10)


### Bug Fixes

* 修复TD511SVT机型写频时，机型码识别错误 ([952094e](https://git.kicad99.com/bfdx/bf8100-web/commit/952094eade1c54f25d37f71ae9fdef7e26fd8468))
* 开放虚拟集群对讲机树形"设备状态"右键菜单 ([e85cf03](https://git.kicad99.com/bfdx/bf8100-web/commit/e85cf037f2d9d3d67ae6f319d3fe2ffc253c955d))

### [2.36.1](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.36.0...v2.36.1) (2022-04-29)


### Bug Fixes

* 删除旧的显示帮助菜单逻辑 ([9dd033c](https://git.kicad99.com/bfdx/bf8100-web/commit/9dd033c8b3e25f1fc822b44e186a09b03a1259e7))
* 设置menuControl配置，控制帮助菜单选项是否显示 ([2ee992b](https://git.kicad99.com/bfdx/bf8100-web/commit/2ee992b279733b6894f09859abed25cd9994d272))
* 设置网站标题和滚动配置，替换默认的i18n翻译内容 ([708a4f2](https://git.kicad99.com/bfdx/bf8100-web/commit/708a4f2ca4f01aea4ec945348a897ce6efd74f7f))

## [2.36.0](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.35.28...v2.36.0) (2022-04-26)


### Features

* TD511SVT机型写频信道的联网参数与虚拟集群互斥(2选1)，使用下拉列表的方式处理 ([ec09ddd](https://git.kicad99.com/bfdx/bf8100-web/commit/ec09ddda966d8045ceff43942384f847d57faa9d))
* TD511SVT机型写频功能,信道添加虚拟集群开关设置 ([0476486](https://git.kicad99.com/bfdx/bf8100-web/commit/047648652ded7c4d407516aaf02dfdbea6ff033c))
* TD511SVT机型写频功能添加虚拟集群配置 ([b382279](https://git.kicad99.com/bfdx/bf8100-web/commit/b38227958022b52793b94d766908b6431592acde))
* 以511SDCFR机型为模板，添加511SVT00机型写频组件 ([87a2eb9](https://git.kicad99.com/bfdx/bf8100-web/commit/87a2eb9e177cae68f3141103b7ead5b533158b40))
* 添加TD511(SVT)机型协议文档 ([1797bce](https://git.kicad99.com/bfdx/bf8100-web/commit/1797bce8a6bfe2996655f6e21cf09d03035025ed))
* 添加TD511SVT机型区域配置 ([de4a8a9](https://git.kicad99.com/bfdx/bf8100-web/commit/de4a8a9a49c51a0630a0a9d657a74fe42d32dc0b))
* 生成TD511SVT机型码信息 ([c33a30f](https://git.kicad99.com/bfdx/bf8100-web/commit/c33a30f9847840d41f3779bb555bb035c05843ee))


### Bug Fixes

* TD510、TD511系列机型写频，信道参数“允许脱网”只在数字信道下显示 ([ce53baa](https://git.kicad99.com/bfdx/bf8100-web/commit/ce53baa837a8a2403e193a4ea9c9d9f6551457c9))
* 修复TD510和TD511系列写频功能的信道参数"TDMA直通模式"禁用错误 ([3af0fbb](https://git.kicad99.com/bfdx/bf8100-web/commit/3af0fbbc2e9deb235accc5c6e01db00f95cf20db))
* 修复TD510和TD511系列写频功能的信道参数"允许脱网标志"禁用错误 ([ae5bf02](https://git.kicad99.com/bfdx/bf8100-web/commit/ae5bf024976fe96b4bc5aa50ea2a1a048c6192b1))

### [2.35.28](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.35.27...v2.35.28) (2022-04-19)


### Bug Fixes

* 修复设备管理添加的虚拟终端，没有设置"devGroup"参数导致终端显示归属组异常问题 ([d99dac9](https://git.kicad99.com/bfdx/bf8100-web/commit/d99dac938508fdb4b409d4233ed700813e8ae7fd))

### [2.35.27](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.35.26...v2.35.27) (2022-04-19)


### Bug Fixes

* 修改集群中继原"自动发送信标时长"、"自动发送信标间隔"为"测机信号时长"、"测机信号间隔" ([e723363](https://git.kicad99.com/bfdx/bf8100-web/commit/e723363355d5f23dd9f03719cc46b7c96a2e25cb))
* 修改集群中继状态显示文本及颜色 ([cd94271](https://git.kicad99.com/bfdx/bf8100-web/commit/cd942710e2f1979f2def542d124d8cb1d763e345))

### [2.35.26](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.35.25...v2.35.26) (2022-04-13)


### Bug Fixes

* 设备管理的虚拟集群控制器最大32个时隙 ([26bf261](https://git.kicad99.com/bfdx/bf8100-web/commit/26bf2618d604cdeb96e1843eddb057927f108621))

### [2.35.25](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.35.24...v2.35.25) (2022-04-12)


### Bug Fixes

* 修复短信历史目标没有显示对应的"全呼"，没有显示已经不存在的dmrId问题 ([62dbfbb](https://git.kicad99.com/bfdx/bf8100-web/commit/62dbfbbb98fddfb7dffaa1fb29d81df5b0618d61))
* 修复联网通话接收到的短信没有正确显示发送人的问题 ([a7e06a2](https://git.kicad99.com/bfdx/bf8100-web/commit/a7e06a2436f4eb273167797ee761923f673ad500))
* 修复虚拟集群中继上线后，没有正确挂载到对应的集群控制器下问题 ([6d4ebdf](https://git.kicad99.com/bfdx/bf8100-web/commit/6d4ebdf8a64b3c98d3a6f29746478204f96a1ce4))
* 修复虚拟集群中继写频的常规设置配置的自动发送信标时长和间隔的参数范围 ([20d2703](https://git.kicad99.com/bfdx/bf8100-web/commit/20d2703464b2de86d94b1494e2cb0fdde85696cb))

### [2.35.24](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.35.23...v2.35.24) (2022-04-11)


### Bug Fixes

* 修改虚拟集群中继常规设置写入方法没有导入模块错误 ([4050c93](https://git.kicad99.com/bfdx/bf8100-web/commit/4050c93c3fdd01751cb81bda94994ce50b206246))

### [2.35.23](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.35.22...v2.35.23) (2022-04-11)


### Bug Fixes

* 修复中继写频在没有接收到响应前就取消事件订阅错误 ([fbcbc4d](https://git.kicad99.com/bfdx/bf8100-web/commit/fbcbc4d870c84cf2f57d31a75217c2ce2a6de7c6))
* 修改虚拟中继写频，"是否加入集群"改为"是否自由中继" ([854629b](https://git.kicad99.com/bfdx/bf8100-web/commit/854629b46d28eff46c45424d5671986ac8249251))

### [2.35.22](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.35.21...v2.35.22) (2022-04-11)


### Bug Fixes

* 修复虚拟集群中继写频，查询状态信息指令调用错误 ([2b424ab](https://git.kicad99.com/bfdx/bf8100-web/commit/2b424abfb4a594187fe951ea64be791b760c57de))

### [2.35.21](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.35.20...v2.35.21) (2022-04-11)


### Bug Fixes

* 修复logo被系统设置的logo的空值覆盖问题 ([643b4a0](https://git.kicad99.com/bfdx/bf8100-web/commit/643b4a0c266e9f5df59764c34309603b59409746))

### [2.35.20](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.35.19...v2.35.20) (2022-04-08)


### Bug Fixes

* 修复接收组组件选中样式缺失错误 ([6192711](https://git.kicad99.com/bfdx/bf8100-web/commit/6192711f59f493183439ac3ebe2dca1abc43b547))
* 虚拟集群手台开放信道管理设置，收听组默认包含归属组 ([d68944e](https://git.kicad99.com/bfdx/bf8100-web/commit/d68944e8e4b70650a95ff29f22699b4b16aec5aa))

### [2.35.19](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.35.18...v2.35.19) (2022-04-08)


### Bug Fixes

* 修改虚拟集群控制器型号为DZ1481 ([746c65c](https://git.kicad99.com/bfdx/bf8100-web/commit/746c65cf8d97aa187aa421ec5493abf2483518e0))

### [2.35.18](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.35.17...v2.35.18) (2022-04-07)


### Bug Fixes

* 修正机型模块导出的车载台机型名称 ([5149044](https://git.kicad99.com/bfdx/bf8100-web/commit/5149044d888cae459c5133660cbe937a2ed39673))
* 修正版本号显示，部分机型的版本没有4个字节 ([7024d99](https://git.kicad99.com/bfdx/bf8100-web/commit/7024d990a42a5c3d614dfc51d8cc22eb275cf952))

### [2.35.17](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.35.16...v2.35.17) (2022-04-06)


### Bug Fixes

* 修改控制器管理在中文环境下表单label的长度 ([11d44b2](https://git.kicad99.com/bfdx/bf8100-web/commit/11d44b253673efc1b4690379752e0909ca28d029))
* 修改控制器类型列表，提取为枚举对象 ([9a1ae79](https://git.kicad99.com/bfdx/bf8100-web/commit/9a1ae79322ea134164ca6a35fcc2c9e3845caf2d))
* 同播中继和虚拟集群中继类型设备，上级控制器为必填 ([0d9385c](https://git.kicad99.com/bfdx/bf8100-web/commit/0d9385c485daf93e3105d5ddf8a23d25177a629c))
* 设备添加失败，尝试删除已经创建的虚拟终端 ([e479e61](https://git.kicad99.com/bfdx/bf8100-web/commit/e479e610b66284f8ca29939a8ba4f4ea7f7a7cf2))

### [2.35.16](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.35.15...v2.35.16) (2022-04-06)


### Bug Fixes

* 对讲机写频，常规设置中语言选择根据支持的语言显示对应的选项 ([a119f7a](https://git.kicad99.com/bfdx/bf8100-web/commit/a119f7a127bcd2bc6de8e7e341de5fb812c2d011))
* 对讲机写频语言类型参数根据配置自动显示或隐藏选项 ([8cf3be0](https://git.kicad99.com/bfdx/bf8100-web/commit/8cf3be0cb5601215978544664ae666f7ad3cc683))
* 当支持的语言只有一种时，不需要显示切换语言菜单选项 ([b40fb3f](https://git.kicad99.com/bfdx/bf8100-web/commit/b40fb3f1575f73488a5294a2e249cf7c207a995b))
* 支持自定义系统滚动标题 ([88d8d9a](https://git.kicad99.com/bfdx/bf8100-web/commit/88d8d9a87e6088d9963bca8f2a06ed92f7faa4b6))
* 添加自定义机型名称注释说明 ([716c0f8](https://git.kicad99.com/bfdx/bf8100-web/commit/716c0f8256b97d6f078b49c66de885c78f653e44))
* 添加隐藏对讲机写频中语言类型参数设置功能 ([52af718](https://git.kicad99.com/bfdx/bf8100-web/commit/52af7189c210893128eede0881bb1f3c25272a14))

### [2.35.15](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.35.14...v2.35.15) (2022-04-06)


### Bug Fixes

* 中继写频显示自定义机型名称 ([a1a13d9](https://git.kicad99.com/bfdx/bf8100-web/commit/a1a13d90a01002e68b971ee55526cd6533e489a7))
* 处理对讲机写频显示机型配置中的自定义机型名称 ([c4c41cb](https://git.kicad99.com/bfdx/bf8100-web/commit/c4c41cbc4c742fc66cecf87832c1fddd5d27bd63))
* 添加中继设备机型码常量 ([6bb51df](https://git.kicad99.com/bfdx/bf8100-web/commit/6bb51df2a883851d056b2a76c0a9425a76b05c21))
* 添加解析机型配置模块 ([3bb0e68](https://git.kicad99.com/bfdx/bf8100-web/commit/3bb0e68449647cacdcf738a664f4b9367d7eaa6d))
* 终端管理的信道管理的区域配置，只显示当前支持的机型 ([838a083](https://git.kicad99.com/bfdx/bf8100-web/commit/838a083729b010d4f58f2204f92236d38e1da355))

### [2.35.14](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.35.13...v2.35.14) (2022-03-23)


### Bug Fixes

* 修复虚拟集群终端归属组在更新页显示上一个终端的数据问题 ([cd0f2b1](https://git.kicad99.com/bfdx/bf8100-web/commit/cd0f2b1b4ff97dcb3257032384e3dbfdfa8d237b))
* 添加虚拟集群终端归属组表单规则 ([a9d7cd7](https://git.kicad99.com/bfdx/bf8100-web/commit/a9d7cd7983c72bfaa391e8fb57ce6c7461a86251))

### [2.35.13](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.35.12...v2.35.13) (2022-03-22)


### Bug Fixes

* add dmrId2RidCaller method ([7b560c3](https://git.kicad99.com/bfdx/bf8100-web/commit/7b560c3422c1974f33040f2e468db3d9bfadf4b0))
* TD930(SVT)机型加入支持写频列表中，处理虚拟集群归属组参数 ([bff8194](https://git.kicad99.com/bfdx/bf8100-web/commit/bff8194e7df7f139d5e5c6cb4a671f65f78b452e))
* 修改TD930(SVT)虚拟集群写频配置 ([573ba65](https://git.kicad99.com/bfdx/bf8100-web/commit/573ba6517dc3d9174c1ca60730a272dac9358b75))
* 修改TD930SVT机型写频的数字信道的虚拟集群配置关联设置 ([42278d3](https://git.kicad99.com/bfdx/bf8100-web/commit/42278d3fd5d3142d3a467cce27994dc1b2b8da77))

### [2.35.12](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.35.11...v2.35.12) (2022-03-22)


### Bug Fixes

* update proto ([0c0c708](https://git.kicad99.com/bfdx/bf8100-web/commit/0c0c708e4716bad03b7c4e922448c42a3b5637d5))
* 终端添加虚拟集群类型终端，并绑定归属组 ([f537a4a](https://git.kicad99.com/bfdx/bf8100-web/commit/f537a4a2471a4e95bc6c31a71811eba23810fc4d))

### [2.35.11](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.35.10...v2.35.11) (2022-03-15)


### Bug Fixes

* add missing BF-TD800(SDC) model ([b57fe34](https://git.kicad99.com/bfdx/bf8100-web/commit/b57fe34ed0af681357a805410ce5447305055812))

### [2.35.10](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.35.9...v2.35.10) (2022-03-14)


### Bug Fixes

* fix deviceInfo.vue firmwareVersion computed prop error ([a51f7a8](https://git.kicad99.com/bfdx/bf8100-web/commit/a51f7a82b61cf5ff27c83adba0dc3d8f73afe1f5))

### [2.35.9](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.35.8...v2.35.9) (2022-03-14)


### Bug Fixes

* QWebChannel connect url error ([52d383b](https://git.kicad99.com/bfdx/bf8100-web/commit/52d383b1d2d8033b0d8f9c31f866456f1e0d56d6))

### [2.35.8](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.35.7...v2.35.8) (2022-03-09)


### Bug Fixes

* 修改终端管理的机型码，改用模块变量 ([cd5f94a](https://git.kicad99.com/bfdx/bf8100-web/commit/cd5f94a2fe1b643769864fd03a1d0ade19c5a039))
* 修正TD511SDC的S70芯片机型版本显示，显示4段的版本号 ([98f44a8](https://git.kicad99.com/bfdx/bf8100-web/commit/98f44a89411851b9f072acf676bac6929408670e))
* 修正TD910SDC、TD910PSDC机型码错误，和TD511SDC的S70芯片机型码显示错误 ([12d9af8](https://git.kicad99.com/bfdx/bf8100-web/commit/12d9af865d9641cf5e51035cbd954cb98d66f2cc))
* 删除废弃的机型写频组件 ([60d4816](https://git.kicad99.com/bfdx/bf8100-web/commit/60d48164881dbfaa05290816d2cce661ce77c032))
* 对讲机写频入口组件，机型码改用模块变量 ([66aaf8c](https://git.kicad99.com/bfdx/bf8100-web/commit/66aaf8c5eeadf37bfa054d258e06242f0b88db08))

### [2.35.7](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.35.6...v2.35.7) (2022-03-07)


### Bug Fixes

* 511S70xx最后2位功能码，需要适配 ([9884f5e](https://git.kicad99.com/bfdx/bf8100-web/commit/9884f5e06cd8266315bb610099e75ec4adf4b607))

### [2.35.6](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.35.5...v2.35.6) (2022-03-07)


### Bug Fixes

* 修复新加机型匹配时因循环导入模块导致程序解析异常错误 ([375dbf4](https://git.kicad99.com/bfdx/bf8100-web/commit/375dbf4e08e7b9951d79157112762b807342832e))

### [2.35.5](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.35.4...v2.35.5) (2022-03-07)


### Bug Fixes

* 修改机型检测方法，TD511SDC,TD910SDC,TD910PSDC添加新的机型码匹配 ([3d998ac](https://git.kicad99.com/bfdx/bf8100-web/commit/3d998ac84233af6dd55b811bec3a2f71d51b7561))

### [2.35.4](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.35.3...v2.35.4) (2022-02-18)


### Bug Fixes

* 修改用户权限显示顺序 ([afafeff](https://git.kicad99.com/bfdx/bf8100-web/commit/afafeffd0c62054d92e8c8d2742dc74a037c46e9))
* 支持插话功能的中继，在选中插话功能后，如果已经存在相同dmrId的终端，则停止操作并警告提示 ([0482b10](https://git.kicad99.com/bfdx/bf8100-web/commit/0482b10c9fcd38f9837ce50f0bb438ad22fca657))
* 终端管理，中继虚拟终端由带插话功能的中继自动创建，需要禁用该选项，防止用户手动创建该类型终端 ([abcd2d4](https://git.kicad99.com/bfdx/bf8100-web/commit/abcd2d4e668b436d43e4dae9113c9890ded8676b))

### [2.35.3](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.35.2...v2.35.3) (2022-01-22)


### Bug Fixes

* 用户缓存的语言已经不在支持列表中，则使用默认的语言 ([88a655e](https://git.kicad99.com/bfdx/bf8100-web/commit/88a655e8e550bcb1762bfbdc7f7472b3177c9eab))

### [2.35.2](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.35.1...v2.35.2) (2022-01-22)


### Bug Fixes

* add comment for model config ([f5fe27d](https://git.kicad99.com/bfdx/bf8100-web/commit/f5fe27d831be689da432684c8ecc89e7e3652951))

### [2.35.1](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.35.0...v2.35.1) (2022-01-22)


### Bug Fixes

* restore default model & language config ([bf64b9a](https://git.kicad99.com/bfdx/bf8100-web/commit/bf64b9ad0131fddcda15184b0d55a3688d2710ba))

## [2.35.0](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.34.0...v2.35.0) (2022-01-21)


### Features

* 添加定制机型支持配置功能 ([dd25d4c](https://git.kicad99.com/bfdx/bf8100-web/commit/dd25d4c4fceb76958273647a975fcf3e38fdae5b))
* 添加系统支持语言的配置功能 ([abfa61f](https://git.kicad99.com/bfdx/bf8100-web/commit/abfa61f1aa37325620cb545725c33ba3b47276b4))

## [2.34.0](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.33.0...v2.34.0) (2022-01-21)


### Features

* 修改法语版本手台、车台、中继名称 ([aa3e021](https://git.kicad99.com/bfdx/bf8100-web/commit/aa3e021357bf0b27cd29b134bfc7dedf5cd728e0))

## [2.33.0](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.32.3...v2.33.0) (2022-01-21)


### Features

* 添加BFP-TM8250FR法语版本写频功能 ([dedbcfa](https://git.kicad99.com/bfdx/bf8100-web/commit/dedbcfa58c0dcaf7ba52e6258778b050efbc471b))
* 添加BFP-TR900FR中继法语版本写频功能 ([314df78](https://git.kicad99.com/bfdx/bf8100-web/commit/314df78a7d8b741a218189fdb229e9aefb0fa815))


### Bug Fixes

* 添加BFP-TM8250FR和BFP-TR900FR设备机型信息 ([1fc1bcc](https://git.kicad99.com/bfdx/bf8100-web/commit/1fc1bcc32dce5c4bca5345d69d14f230d9f29328))

### [2.32.3](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.32.2...v2.32.3) (2022-01-20)


### Bug Fixes

* 修复法语下数据管理等一些表单的标签宽度不足问题 ([241fea2](https://git.kicad99.com/bfdx/bf8100-web/commit/241fea2f66881dec874d0bd7635a5d97cfe1dd2c))
* 终端管理，关于SIP网关类型终端，禁用选择，由系统在配置SIP网关中继时自动创建 ([1ad4263](https://git.kicad99.com/bfdx/bf8100-web/commit/1ad4263edc0a0f4c2e64e6ff3ef833de72ec8eed))

### [2.32.2](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.32.1...v2.32.2) (2022-01-20)


### Bug Fixes

* 屏蔽TD511(SDC)FR机型写频，常规设置页中关于语音加密配置项 ([3f46ae5](https://git.kicad99.com/bfdx/bf8100-web/commit/3f46ae5dc2869c7f71253c4bef8ed61b17168cef))
* 确认TD511(SDC)FR机型名称为"BFP-TD511FR" ([6b5a9cf](https://git.kicad99.com/bfdx/bf8100-web/commit/6b5a9cf93216ad2f3287f0f355fc5b3e4ec0d678))

### [2.32.1](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.32.0...v2.32.1) (2022-01-20)


### Bug Fixes

* 修复对讲机写频功能的数字报警、漫游、扫描等数据列表，在添加自定义数据后重新读取终端数据时，因没有清除添加的自定义数据，导致读取终端数据后出现重复ID的数据及超出协议定义上限无法正常写入数据问题 ([3ffd747](https://git.kicad99.com/bfdx/bf8100-web/commit/3ffd747c9175366370ab9053c6ba309bb650e32c))

## [2.32.0](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.31.4...v2.32.0) (2022-01-19)


### Features

* add TD511(SDC)FR channel encryption settings ([228d592](https://git.kicad99.com/bfdx/bf8100-web/commit/228d592fa65cb76631d335d37ec61de85b10c620))
* add TD511(SDC)FR zone info ([3193173](https://git.kicad99.com/bfdx/bf8100-web/commit/3193173c6cfb2d8f8ba71ce3828f807b7221602f))
* finish TD511(SDC)FR write frequency read & write func ([066e7c2](https://git.kicad99.com/bfdx/bf8100-web/commit/066e7c22bd64b4ad1b42261621cb0846c250a163))
* take TD511(SDC) as the template, add TD511(SDC)FR write frequency component ([4705edd](https://git.kicad99.com/bfdx/bf8100-web/commit/4705edd2956b54361c08e277e67ea33f25142aa7))
* TD511(SDC)FR locale setting replace CN to FR ([d98d176](https://git.kicad99.com/bfdx/bf8100-web/commit/d98d176f32dedca4a504cfec490c6c1b033531ed))


### Bug Fixes

* delete unused global var and lint code style ([d3f0d43](https://git.kicad99.com/bfdx/bf8100-web/commit/d3f0d43c106b484b9b4451783d0e4fa4b663995d))
* fixed an issue with French encryption list action button width,TM8250(SDC), TD930(SDC), TD930(SVT), TD910(SDC), TD910P(SDC) ([6caa9fe](https://git.kicad99.com/bfdx/bf8100-web/commit/6caa9fed86029ceebc99afab32d46b070ad6406c))
* modelInfo.js module support function code for "getModelName" method ([e31e053](https://git.kicad99.com/bfdx/bf8100-web/commit/e31e0536561f15bf2fbcf878e0b9964989003ce7))

### [2.31.4](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.31.3...v2.31.4) (2022-01-17)


### Bug Fixes

* add French emergency wav ([9bc4af9](https://git.kicad99.com/bfdx/bf8100-web/commit/9bc4af99945c9ba944920aea4bc14d7624c26634))
* add missing curd cmd label for line master ([dc51289](https://git.kicad99.com/bfdx/bf8100-web/commit/dc512899bb361845354530d4886d73ec6b43dbe2))
* adjust alert dialog layout in French env ([47376fc](https://git.kicad99.com/bfdx/bf8100-web/commit/47376fc85a5da49262899ea5e7493d049ffe617d))
* adjust command menu layout in French env ([9219170](https://git.kicad99.com/bfdx/bf8100-web/commit/9219170c452c647296929a3f5ba0a04f609b27d3))
* adjust data type components layout in French env ([7b780d0](https://git.kicad99.com/bfdx/bf8100-web/commit/7b780d001809a27450af40807f65f19ee9122d40))
* adjust help menu layout in French env ([fb7d4fb](https://git.kicad99.com/bfdx/bf8100-web/commit/fb7d4fbea810b0ecf17080a4f09e827d26f0d26e))
* adjust history components layout in French env ([8be1fb0](https://git.kicad99.com/bfdx/bf8100-web/commit/8be1fb08acaff9cdf860db892b3fe072812fe7f5))
* adjust repeater write frequency layout in French env ([956422f](https://git.kicad99.com/bfdx/bf8100-web/commit/956422f3bcab5be0430b6194fca1b1dd4cafac8e))
* adjust roam & scan common styles ([d29b1cd](https://git.kicad99.com/bfdx/bf8100-web/commit/d29b1cde6348eb138060c4cb59fa77c4cfab5041))
* adjust TD511(SDC) write frequency layout in French env ([600b6ff](https://git.kicad99.com/bfdx/bf8100-web/commit/600b6ff18f7f5d4e2754a7eb437b837750d13d53))
* adjust TD800(SDC) & TD510(SDC) write frequency layout in French env ([736c81c](https://git.kicad99.com/bfdx/bf8100-web/commit/736c81c418f3733f2b2b9dea45f095d146c21eef))
* adjust TD910(SDC) write frequency layout in French env ([7519e48](https://git.kicad99.com/bfdx/bf8100-web/commit/7519e4886c368fa7dcf532e2b2ef8539ddc47bca))
* adjust TD910P(SDC) write frequency layout in French env ([deba1a2](https://git.kicad99.com/bfdx/bf8100-web/commit/deba1a25e57969d29d6728a773d631de6f01e23a))
* adjust TD930(SDC) write frequency layout in French env ([adfd7ad](https://git.kicad99.com/bfdx/bf8100-web/commit/adfd7ad16f99574594324b501ed3d9198efa9b97))
* adjust TD930(SVT) write frequency layout in French env ([55c6b9b](https://git.kicad99.com/bfdx/bf8100-web/commit/55c6b9b15eb21de8dfcb77cce720fcc26df33323))
* adjust TM8250(SDC) write frequency layout in French env ([8589769](https://git.kicad99.com/bfdx/bf8100-web/commit/8589769141fcd1f451e484d40f692a758d392eaa))
* datatable info tips in French env ([d5974b9](https://git.kicad99.com/bfdx/bf8100-web/commit/d5974b9d4fd4459f56c6b69698ee6a0e93e27590))
* datatables component add tooltip when content overflow ([f8c5cce](https://git.kicad99.com/bfdx/bf8100-web/commit/f8c5ccee378c5551ef745341b92e4ceab9e12b14))
* delete console.log func ([cb7a976](https://git.kicad99.com/bfdx/bf8100-web/commit/cb7a97654154dde494087fb385cbc978f15459f9))
* delete extra space in i18n key ([113b5cd](https://git.kicad99.com/bfdx/bf8100-web/commit/113b5cd3c321c544bbe158806947c83e1a057866))
* delete extra space in i18n values ([f348566](https://git.kicad99.com/bfdx/bf8100-web/commit/f348566d88979e9263161185ce8cd3559a98234e))
* extract roam & scan common styles ([42fc957](https://git.kicad99.com/bfdx/bf8100-web/commit/42fc957e96edc59e5d8822498c5b80f32b95c1f4))
* fix zh-CN help pdf name error ([535ef68](https://git.kicad99.com/bfdx/bf8100-web/commit/535ef68d05bce0e4cdb4cd8086798da4e7671840))
* fix zh-CN wav name error ([419d16c](https://git.kicad99.com/bfdx/bf8100-web/commit/419d16cca1798241701bef5e89690f442ecedb37))
* modify fr.json ([4a76df5](https://git.kicad99.com/bfdx/bf8100-web/commit/4a76df55c9f77c6877e42a6f4afc9ec5c9d3a611))
* replace VueMixin with repeaterWfMixin ([43d77dc](https://git.kicad99.com/bfdx/bf8100-web/commit/43d77dc9dd233ecca4c31bf99de337c4f2b348fa))
* reset "visible" default value on DZ1480.vue ([6d8e113](https://git.kicad99.com/bfdx/bf8100-web/commit/6d8e11304950193cd3f70858853bae31ac32c623))
* reset element-ui el-form label & checkbox css ([aecc44a](https://git.kicad99.com/bfdx/bf8100-web/commit/aecc44a20f1b63e0bf9cc101b217b35509aa8e53))
* TD800(SDC) replace fullscreen with isFullscreen ([2eb113f](https://git.kicad99.com/bfdx/bf8100-web/commit/2eb113fdbc583f583fde39fcbb223289582e4433))
* update fr.json ([e45dce6](https://git.kicad99.com/bfdx/bf8100-web/commit/e45dce6352bbc43e4119361206347cd6e4f9ae8c))

### [2.31.3](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.31.2...v2.31.3) (2022-01-07)


### Bug Fixes

* add login background image with French ([4baab38](https://git.kicad99.com/bfdx/bf8100-web/commit/4baab38ebfe12d9fba125e70612d9b935b33280a))
* update fr.json ([793508b](https://git.kicad99.com/bfdx/bf8100-web/commit/793508b901e0d1ec6535e1787dd88592be8ce441))

### [2.31.2](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.31.1...v2.31.2) (2022-01-05)


### Bug Fixes

* sound history can not play again ([8e38433](https://git.kicad99.com/bfdx/bf8100-web/commit/8e384339acf837cfda56d7d608d1aacbbbfde7bd))

### [2.31.1](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.31.0...v2.31.1) (2022-01-05)


### Bug Fixes

* mapbox-gl添加支持法语资源请求 ([82eb8a9](https://git.kicad99.com/bfdx/bf8100-web/commit/82eb8a9551e14f8b5bc50dc14e7b9779bd4e3856))
* 修改组件中使用的'cn'字符串，改为使用定义的语言变量 ([46e0a6f](https://git.kicad99.com/bfdx/bf8100-web/commit/46e0a6ff731ab677b5cd577fff9f3d403d7cb45b))
* 添加支持法语翻译，修改cn.json为zh-CN.json ([19aca38](https://git.kicad99.com/bfdx/bf8100-web/commit/19aca3865bf10e6de2932a50f37c21ed61e23202))
* 登录页添加默认的背景图，以适配不同语言 ([1471189](https://git.kicad99.com/bfdx/bf8100-web/commit/1471189c12cd19aba32304ce47d2a272dc3b50da))

## [2.31.0](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.30.1...v2.31.0) (2021-11-18)


### Features

* 添加型号为"TR850M"的新设备写频功能 ([c2eb8b1](https://git.kicad99.com/bfdx/bf8100-web/commit/c2eb8b115ddde136d89c9c516782257587b17a37))

### [2.30.1](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.30.0...v2.30.1) (2021-11-17)


### Bug Fixes

* 修改自动打包job，先下载bf8100pkg仓库下的.gitlab-ci.yml文件再推送 ([468073c](https://git.kicad99.com/bfdx/bf8100-web/commit/468073c8aeea2dbd3ff286bc8e07649a49275dda))

## [2.30.0](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.29.15...v2.30.0) (2021-11-05)


### Features

* 修改用户权限树单位节点选择交互逻辑，允许包含子级单位 ([bc5f97d](https://git.kicad99.com/bfdx/bf8100-web/commit/bc5f97d75ddf3c9acaa7aea4110a9c751771910e))


### Bug Fixes

* 更新iconfont字体图标库 ([a05511d](https://git.kicad99.com/bfdx/bf8100-web/commit/a05511d3f04df04025007405af4ef66c41cbb7cf))

### [2.29.15](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.29.14...v2.29.15) (2021-11-02)


### Bug Fixes

* use pkg-push job, sync build result to Bf8100pkg repo ([c66ca29](https://git.kicad99.com/bfdx/bf8100-web/commit/c66ca2957304b6214213a78f8b298b662d5c9c67))

### [2.29.14](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.29.13...v2.29.14) (2021-10-29)


### Bug Fixes

* use node:14.18.1 image, fix node-sass not supported node:16.x.x ([b5eb275](https://git.kicad99.com/bfdx/bf8100-web/commit/b5eb275a7bd6655cd450f862bda3466a2775ec74))

### [2.29.13](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.29.12...v2.29.13) (2021-10-13)


### Bug Fixes

* **TD910P(SDC):** 数字报警屏蔽自动发送GPS参数，该功能暂未实现 ([af1af9d](https://git.kicad99.com/bfdx/bf8100-web/commit/af1af9dd337ac1142dc6d56e4fd744222e856e20))

### [2.29.12](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.29.11...v2.29.12) (2021-10-09)


### Bug Fixes

* 录音历史表格添加源中继列显示 ([f946eac](https://git.kicad99.com/bfdx/bf8100-web/commit/f946eac15652cf467e7509ac018344242c4b85ba))

### [2.29.11](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.29.10...v2.29.11) (2021-10-09)


### Bug Fixes

* **TD910/P(SDC):** 修复扫描组、漫游组在选择终端时没有过滤无效的信道ID错误 ([cec418e](https://git.kicad99.com/bfdx/bf8100-web/commit/cec418e57563db982af622c7a62256ad158a3918))
* **WF:** 修复TD930SDC、TD930SVT、TM825SDC在选择终端时没有对扫描组、漫游组等数据进行过滤无效的信道ID错误 ([2486cc1](https://git.kicad99.com/bfdx/bf8100-web/commit/2486cc1f5414a6e662542e73c0225c70b0f62ff5))
* **WF:** 修改TD510SDC、TD511SDC机型扫描组、漫游组等数据过滤无效信道ID逻辑 ([74409af](https://git.kicad99.com/bfdx/bf8100-web/commit/74409afe60863feb9c356951f476b6782b955995))

### [2.29.10](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.29.9...v2.29.10) (2021-10-08)


### Bug Fixes

* **TD910/P(SDC):** 开放救援配置及信息的写入配置 ([9bdbde8](https://git.kicad99.com/bfdx/bf8100-web/commit/9bdbde8da94a1934e304d6d4c86584d751d17b73))

### [2.29.9](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.29.8...v2.29.9) (2021-10-08)


### Bug Fixes

* **TD910(SDC):** 同步信道模拟信道参数及彩色码范围 ([3a4b0d2](https://git.kicad99.com/bfdx/bf8100-web/commit/3a4b0d21a93eacd75ac4ee7d1cf2e825a67845a4))
* **TD910/P(SDC):** 开机密码输入框添加输入提示 ([2f14da1](https://git.kicad99.com/bfdx/bf8100-web/commit/2f14da1b7aed49207c3518b4b9d642f147bd3ec8))
* **TD910/P(SDC):** 救援信息限制为24个字符 ([a4ee0cd](https://git.kicad99.com/bfdx/bf8100-web/commit/a4ee0cd19327afb4867413b2b0d97248eeb9a9bb))
* **TD910P(SDC):** TDMA直通模式开启后，彩色码最大只能为14 ([126db1b](https://git.kicad99.com/bfdx/bf8100-web/commit/126db1b05411e5c2ad054fc6a2cf44b56d0fdc4e))
* **WF:** TD910(SDC)和TD910P(SDC)机型声控等级最大为9级 ([a6e23f1](https://git.kicad99.com/bfdx/bf8100-web/commit/a6e23f165b1c5232323bd56ef720844959a94255))
* **WF:** TD910P(SDC)模拟信道中屏蔽以下选项：加重、扰频、压扩、拍频 ([36c7fb7](https://git.kicad99.com/bfdx/bf8100-web/commit/36c7fb7d3c2bd6b4de9d319ccc4f3d296f4bea48))
* **WF:** 修正TD910/P(SDC)按键定义选项 ([a0296a0](https://git.kicad99.com/bfdx/bf8100-web/commit/a0296a00c50dabdc0bc31bd015f7ef8c2f157567))

### [2.29.8](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.29.7...v2.29.8) (2021-09-30)


### Bug Fixes

* **datatable:** 修改终端和设备子行详情表的单元格样式，以自适应表格宽度 ([fb4efc0](https://git.kicad99.com/bfdx/bf8100-web/commit/fb4efc03d7ec117481cca2af30ffaf7514215be7))
* **datatable:** 修改表格子行数据展示方式，以popover弹框显示 ([77baa29](https://git.kicad99.com/bfdx/bf8100-web/commit/77baa29af7872cfb098754fbb3db2709ba2610fc))
* **datatable:** 取消表格当前显示的起始、结束行提示 ([3b5ccd7](https://git.kicad99.com/bfdx/bf8100-web/commit/3b5ccd7db19d31df6ede3bfcf6ebed13da37fa91))
* **datatables:** 取消在data()方法中声明datatables实例变量，避免被vue劫持代理增加性能损耗 ([7c12480](https://git.kicad99.com/bfdx/bf8100-web/commit/7c12480384ddc8bd8ca439fdd4acb6abf5967359))
* **datatables:** 添加子行详情popover最大高度，并在更新时同步滚动条高度 ([bfc5fd5](https://git.kicad99.com/bfdx/bf8100-web/commit/bfc5fd5ecbae8143a53aca18d9c4d143dfe4cdca))
* **device:** 批量复制信道功能，树形默认全部折叠 ([c284cb0](https://git.kicad99.com/bfdx/bf8100-web/commit/c284cb08ac11a70daffdaa80aedb515ed953dc0c))
* **mapboxgl:** 加大popup最大宽度限制，修改为480px ([c977fd4](https://git.kicad99.com/bfdx/bf8100-web/commit/c977fd4d18b1611476eaac4499106460889d5a52))

### [2.29.7](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.29.6...v2.29.7) (2021-09-28)


### Bug Fixes

* **org:** 默认账号在创建单位时，屏蔽创建单位地图标记点功能 ([16acb61](https://git.kicad99.com/bfdx/bf8100-web/commit/16acb619a892b3b931cf064c01e2f3e04e70a1f1))

### [2.29.6](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.29.5...v2.29.6) (2021-09-27)


### Bug Fixes

* **TD910P(SDC):** 修复写频功能卫星定位数据写入失败错误，结构没有定位类型值导致编译错误 ([f7e4ee4](https://git.kicad99.com/bfdx/bf8100-web/commit/f7e4ee42b6a76f775da21732eee93d4b5af440a0))

### [2.29.5](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.29.4...v2.29.5) (2021-09-23)


### Bug Fixes

* **eslint:** 忽略dataManage.js模块中no-unused-vars警告 ([06b2f19](https://git.kicad99.com/bfdx/bf8100-web/commit/06b2f1995ebcb239f28c84d483e83c7858dc50c9))
* **linePoint:** 巡查点的rfid值只能是hex字符串 ([63e5004](https://git.kicad99.com/bfdx/bf8100-web/commit/63e50046e1a38a4e613346ec2e9cd89fd5fc3b18))
* **sendCmd:** 遥开/遥闭命令不支持按组下发命令 ([e24a1ef](https://git.kicad99.com/bfdx/bf8100-web/commit/e24a1efdd9d30e28e1f8d0d8ff4f704d8a7b0151))

### [2.29.4](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.29.3...v2.29.4) (2021-09-18)


### Bug Fixes

* SIP网关设备添加或更新时，检测SIP参数是否配置正确 ([20030dd](https://git.kicad99.com/bfdx/bf8100-web/commit/20030dddb7a4f051c2c2e0431de336e7a100cb92))
* SIP网关设备添加提示音语言选择功能 ([e79d208](https://git.kicad99.com/bfdx/bf8100-web/commit/e79d208f3546a96e3a09c3324185582727b8c1c7))

### [2.29.3](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.29.2...v2.29.3) (2021-09-16)


### Bug Fixes

*  网关授权名称长度限制改为16个字符 ([b76b45e](https://git.kicad99.com/bfdx/bf8100-web/commit/b76b45e0a03c3afd60dad9ba516ef0dbf464cda6))
* 添加SIP网关授权 ([a6e92fe](https://git.kicad99.com/bfdx/bf8100-web/commit/a6e92fe97b3fa9812ada3b84254f7067ae82e83b))

### [2.29.2](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.29.1...v2.29.2) (2021-09-15)


### Bug Fixes

* SIP网关终端不允许直接删除，需要删除相关设备后联级删除 ([a14ce55](https://git.kicad99.com/bfdx/bf8100-web/commit/a14ce55ce6ee241c6eb90b5d7ac0e3ed59811452))
* SIP网关配置参数，端口号改用number类型 ([e23c634](https://git.kicad99.com/bfdx/bf8100-web/commit/e23c6340543d926f3d90ad8793d61b03b2367c24))
* upgrade yarn.lock ([91c0ae0](https://git.kicad99.com/bfdx/bf8100-web/commit/91c0ae06ead59f092ed8d4f4ef8a7c8672058cd8))
* 修改sip网关配置参数字段名称 ([03eb9ae](https://git.kicad99.com/bfdx/bf8100-web/commit/03eb9ae0be9bfcb74ada1c0077eb6e5af5bb9947))

### [2.29.1](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.29.0...v2.29.1) (2021-09-10)


### Bug Fixes

* **controller:** 修改SIP网关设备参数设置规则，域名和端口为必填 ([621eb07](https://git.kicad99.com/bfdx/bf8100-web/commit/621eb07055c170de606e8f9fe5f5ee223aaa866d))
* **device:** SIP网关终端开放设置黑白名单 ([4ce120d](https://git.kicad99.com/bfdx/bf8100-web/commit/4ce120d95f5ca7d136febe4e73a6b1a776115cd1))
* **package:** 指令node-sass和sass-loader版本，以适配vue-template-compiler@2.6.12版本 ([b40909b](https://git.kicad99.com/bfdx/bf8100-web/commit/b40909b284e021ca8d85729ecd473e9699855bde))

## [2.29.0](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.28.1...v2.29.0) (2021-09-08)


### Features

* **controller:** 添加"sip网关"类型控制器 ([e81f4e6](https://git.kicad99.com/bfdx/bf8100-web/commit/e81f4e6a0bf4fed6a0f4f43c3c17cc83c469a9fa))
* **controller:** 添加sip网关类型控制器 ([0c00bbf](https://git.kicad99.com/bfdx/bf8100-web/commit/0c00bbf6467158fcf103e4578762656ef715f946))
* **controller:** 添加sip网关表格子行详情信息 ([b900f07](https://git.kicad99.com/bfdx/bf8100-web/commit/b900f0739ef1c2ae9737df868621f70ca27dc2ae))
* **controller:** 添加sip网关配置组件 ([5befc0a](https://git.kicad99.com/bfdx/bf8100-web/commit/5befc0a8544d4b7939d8c056f23b753cfdf87ccb))
* **device:** 终端管理添加sip网关终端类型数据 ([053401b](https://git.kicad99.com/bfdx/bf8100-web/commit/053401b1f891cfdfa3d9e867e36d922bb3208c07))


### Bug Fixes

* **ci:** modify .gitlab-ci.yml ([a214538](https://git.kicad99.com/bfdx/bf8100-web/commit/a214538ec52c58c20d100e5079ed8a7da4641505))
* **ci:** modify .gitlab-ci.yml ([4e1cede](https://git.kicad99.com/bfdx/bf8100-web/commit/4e1cede4240040402b0f3c06e4dfbaeeec156e3f))
* **ci:** 升级node镜像版本为lts，修改sharp包镜像配置 ([127802d](https://git.kicad99.com/bfdx/bf8100-web/commit/127802d54972796e6c9d4ec48ac881621877e563))
* **controller:** 调整控制器添加、更新、删除功能逻辑顺序 ([ac01c2d](https://git.kicad99.com/bfdx/bf8100-web/commit/ac01c2d85e86d4e85d39f2e26f6e47dde311efdd))
* update proto ([2d4bc53](https://git.kicad99.com/bfdx/bf8100-web/commit/2d4bc53fb809b2eb95a3af212ceb9f8d2ec5b245))

### [2.28.1](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.28.0...v2.28.1) (2021-09-03)


### Bug Fixes

* 修改用户权限文本，"坐席全呼权限"改为"全呼权限"，以适应APP ([6792db5](https://git.kicad99.com/bfdx/bf8100-web/commit/6792db54de3cea128d57e2acc7c3cec7a6bda427))

## [2.28.0](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.27.1...v2.28.0) (2021-08-26)


### Features

* TD910P(SDC)写频功能，总体设置添加定位模式选择设置 ([780eba7](https://git.kicad99.com/bfdx/bf8100-web/commit/780eba707f7c0c162c73c9f076c8ce85ac04b1ce))
* TD910P(SDC)写频功能，总体设置添加开机密码设置 ([3014f6e](https://git.kicad99.com/bfdx/bf8100-web/commit/3014f6e245faf9fa3d73e9ae806660c194e54a98))
* TD910P(SDC)写频功能，菜单设置屏蔽部分参数 ([e54aad2](https://git.kicad99.com/bfdx/bf8100-web/commit/e54aad2f7af1266ba99f87161fc9c0857c00fe86))
* TD910P(SDC)机型写频屏蔽救援信道 ([0556921](https://git.kicad99.com/bfdx/bf8100-web/commit/055692117157081964bdf8d398f7d81ff08b1abe))
* 以TD910(SDC)机型为模板，添加TD910P(SDC)写频功能 ([4cf354d](https://git.kicad99.com/bfdx/bf8100-web/commit/4cf354de4414397c25d7d47751d03283d6237a0b))
* 手台写机型"BF-TD910P(SDC)"添加区域管理 ([404e70f](https://git.kicad99.com/bfdx/bf8100-web/commit/404e70fcd7eb91889c2e0ac5de085aa7814e88b9))
* 手台写频添加"BF-TD910P(SDC)"机型选项 ([8efe43e](https://git.kicad99.com/bfdx/bf8100-web/commit/8efe43ea0799a751baf498a37827f3e263996ba8))
* 添加TD910P(SDC)机型码和写频文档 ([7dde914](https://git.kicad99.com/bfdx/bf8100-web/commit/7dde914076b7536e6608d1cf456a923d5e483a9e))


### Bug Fixes

* TD910(SDC)写频数字紧急报警功能，删除不需要的参数传递 ([3f3ff53](https://git.kicad99.com/bfdx/bf8100-web/commit/3f3ff53b363e2d1b805f845687dc9ac7b583e00f))
* 修改写频功能树形菜单updateViewport方法 ([eb79a5d](https://git.kicad99.com/bfdx/bf8100-web/commit/eb79a5d3bd109013f9493c5e6f0b5dc0bf7cbcee))
* 屏蔽TD910(SDC)写频功能救援信道配置页 ([d14b269](https://git.kicad99.com/bfdx/bf8100-web/commit/d14b2694df4a3e845aa7c5f909f7c3da8762748b))
* 手台写频数字紧急报警开放报警时自动发送GPS信息配置 ([1e1fe2a](https://git.kicad99.com/bfdx/bf8100-web/commit/1e1fe2a7475d3c735c25e40c51bdf74b9994ffec))

### [2.27.1](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.27.0...v2.27.1) (2021-08-24)


### Bug Fixes

* DZ1480SVT虚拟集群中继写频功能，常规设置显示密钥 ([82a232e](https://git.kicad99.com/bfdx/bf8100-web/commit/82a232e6300624dbce6e26cc93558f72abaada89))
* TD930SVT机型手台写频，虚拟群集功能屏蔽鉴权参数 ([492b1e9](https://git.kicad99.com/bfdx/bf8100-web/commit/492b1e9fdf5a465d7644dff88302e9ac97f0f121))

## [2.27.0](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.26.2...v2.27.0) (2021-08-19)


### Features

* 控制器管理添加打开虚拟集群中继写频页面功能 ([df08365](https://git.kicad99.com/bfdx/bf8100-web/commit/df083651d0b98f4e4f9f4fa9ea8ef1ee2ad922ea))
* 添加虚拟集群中继常规配置写频 ([72193f7](https://git.kicad99.com/bfdx/bf8100-web/commit/72193f7bee5f4f8cd9b11e1831b462ec6f85dd54))
* 添加虚拟集群控制器(DZ148SVT0)和中继(TR900SVT/TR850SVT)类型 ([0ed64d1](https://git.kicad99.com/bfdx/bf8100-web/commit/0ed64d1a6fcb4e22412391cb955114f160687aee))
* 添加虚拟集群控制器写频操作页面布局 ([5585d13](https://git.kicad99.com/bfdx/bf8100-web/commit/5585d13b6aad8e8b5f490f16f7d2ca48e12fdf6b))
* 虚拟集群中继写频添加状态显示功能 ([6d790ad](https://git.kicad99.com/bfdx/bf8100-web/commit/6d790ad288d675343dba69417cd00512876b9cf0))
* 虚拟集群中继写频的切换功率添加查询功能 ([e792481](https://git.kicad99.com/bfdx/bf8100-web/commit/e79248147ee4375d71a0f05eea1b2fe6e27e981b))
* 订阅虚拟集群控制器上报中继信息的103命令 ([d383b31](https://git.kicad99.com/bfdx/bf8100-web/commit/d383b31a0cb80e348bd9322ccebb20f50201e272))


### Bug Fixes

* update dependencies ([12ae77f](https://git.kicad99.com/bfdx/bf8100-web/commit/12ae77f38a0a2397754ef3f4c56dcd2b66d4cd59))
* 修复同播控制器写频命令没有成功下发指令时，没有正确取消订阅指令响应的事件 ([135f2a6](https://git.kicad99.com/bfdx/bf8100-web/commit/135f2a637eaff911cc526d3b53f7620718c0b8ac))
* 修改控制器数据管理，类型格式化方法 ([328e845](https://git.kicad99.com/bfdx/bf8100-web/commit/328e845eec9d330c7c13936e6288ac24a356bcd7))
* 修改虚拟集群中继重启、查询上报基础信息方法 ([6c814ca](https://git.kicad99.com/bfdx/bf8100-web/commit/6c814ca61b3d27e6dda14220d345a2df9f16e5f5))
* 恢复中继写频默认为TR805005型号 ([8b05e60](https://git.kicad99.com/bfdx/bf8100-web/commit/8b05e60f9514c0746639baace32ef73d2aa56c0f))
* 更新协议 ([b77c6b1](https://git.kicad99.com/bfdx/bf8100-web/commit/b77c6b1b77e3e32b8a1b30a40cf739350fd79e4b))
* 更新虚拟集群控制器机型码 ([47b9aff](https://git.kicad99.com/bfdx/bf8100-web/commit/47b9affb4a75547bfde8b3dc1698db40d658b670))
* 集群中继写频删除信道配置及切换信道功能 ([77ca7e5](https://git.kicad99.com/bfdx/bf8100-web/commit/77ca7e506f75071169b495535e20bb984e641028))

### [2.26.2](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.26.1...v2.26.2) (2021-08-16)


### Bug Fixes

* 修改更新用户的声码器sn不成功错误 ([4fdd2fe](https://git.kicad99.com/bfdx/bf8100-web/commit/4fdd2fe14ab18411ec9c3f82e608a28ef850de3b))

### [2.26.1](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.26.0...v2.26.1) (2021-08-13)


### Bug Fixes

* **User:** 添加App声码器SN配置属性 ([8849258](https://git.kicad99.com/bfdx/bf8100-web/commit/8849258d8c39f44fb5674426e0888236ddc636cc))

## [2.26.0](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.25.8...v2.26.0) (2021-08-02)


### Features

* **TD930SVT:** TD930SVT信道参数添加虚拟集群参数及相关禁用规则 ([bf7d9dc](https://git.kicad99.com/bfdx/bf8100-web/commit/bf7d9dcfb9dfc3cf7af8b64f210068b790af0632))
* **TD930SVT:** TD930SVT虚拟集群参数配置过滤无效的信道ID ([895aa02](https://git.kicad99.com/bfdx/bf8100-web/commit/895aa02b4eb82c2dec56956bcb8794a11e3e7262))
* **TD930SVT:** 为TD930SVT适配区域信息 ([a73bcec](https://git.kicad99.com/bfdx/bf8100-web/commit/a73bcec28c973d972035e220c137952712205242))
* **TD930SVT:** 以TD930SDC机型写频为模板，添加TD930SVT机型写频组件 ([7a3a339](https://git.kicad99.com/bfdx/bf8100-web/commit/7a3a33959667222a397e2e4542e4cdcfd1615601))
* **TD930SVT:** 添加虚拟集群配置页面 ([5feac13](https://git.kicad99.com/bfdx/bf8100-web/commit/5feac13f361e7b7da9daa9b2a44229f47ffa4965))
* 添加TD930SVT机型写频协议文件 ([acea5c1](https://git.kicad99.com/bfdx/bf8100-web/commit/acea5c1a302684f2286aba0faeb32bfaab3158d2))


### Bug Fixes

* **tree:** 修复设备列表树折叠隐藏后，切换按钮鼠标悬停的样式被覆盖问题 ([fa1ea36](https://git.kicad99.com/bfdx/bf8100-web/commit/fa1ea36a69f2e2a9a87b165bb7bb9805b026ea71))

### [2.25.8](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.25.7...v2.25.8) (2021-07-14)


### Bug Fixes

* bc15超时处理，如果是坐席讲话，则使用坐席的超时时间 ([502b2d3](https://git.kicad99.com/bfdx/bf8100-web/commit/502b2d30ad6acc3fbeeaa92052c1c6915ad7f2ab))

### [2.25.7](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.25.6...v2.25.7) (2021-06-29)


### Bug Fixes

* **device:** 添加请求333命令，更新在线终端的最后数据时间，以同步在线状态 ([60f70ac](https://git.kicad99.com/bfdx/bf8100-web/commit/60f70ac4135c06393069919522e0bda2a2f58ec5))

### [2.25.6](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.25.5...v2.25.6) (2021-06-23)


### Bug Fixes

* 更新中文版本帮助文档 ([fd1aeae](https://git.kicad99.com/bfdx/bf8100-web/commit/fd1aeaeae655ee84385aebe8f41231c907afd4fa))

### [2.25.5](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.25.4...v2.25.5) (2021-06-18)


### Bug Fixes

* 相关软件请求url添加时间参数，避免有缓存无法请求最新数据 ([fa7b0b5](https://git.kicad99.com/bfdx/bf8100-web/commit/fa7b0b54b4e3ea0297d432e5a4a90bfac77e4dd5))

### [2.25.4](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.25.3...v2.25.4) (2021-06-15)


### Bug Fixes

* 取消ambe播放器下载，该软件需要使用usb3000声码器，使用系统TC918或asac版本即可 ([6f71343](https://git.kicad99.com/bfdx/bf8100-web/commit/6f7134397bfe397b77b11a6743810b614a94f1e0))

### [2.25.3](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.25.2...v2.25.3) (2021-06-15)


### Bug Fixes

* 设备管理的设备名称允许输入中文 ([843e7fd](https://git.kicad99.com/bfdx/bf8100-web/commit/843e7fd7b33a9e527fe3255c29096f76c3da4383))

### [2.25.2](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.25.1...v2.25.2) (2021-06-12)


### Bug Fixes

* 修复地图标记点不能适应自定义的大小问题 ([98764de](https://git.kicad99.com/bfdx/bf8100-web/commit/98764de2641e1b1c407d35e92e8f054d0a0a087c))

### [2.25.1](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.25.0...v2.25.1) (2021-06-08)


### Bug Fixes

* 修改控制器字体图标及其大小 ([a3edfeb](https://git.kicad99.com/bfdx/bf8100-web/commit/a3edfeb726ea36a729227bf76ed4aabddd502e2e))
* 更新字体图标 ([35605de](https://git.kicad99.com/bfdx/bf8100-web/commit/35605de0a32cda06dce537adb43151529ebc6097))

## [2.25.0](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.24.4...v2.25.0) (2021-05-28)


### Features

* add mobile device type ([e7dca43](https://git.kicad99.com/bfdx/bf8100-web/commit/e7dca434b2d97c847fe75df33255dd0098379c95))

### [2.24.4](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.24.3...v2.24.4) (2021-05-07)


### Bug Fixes

* **device tree:** 修复树形加载单位节点，没有过滤root单位，导致默认账号登录时无法加载树形组件 ([bf522b2](https://git.kicad99.com/bfdx/bf8100-web/commit/bf522b2f3efc7c9d7a46df6a73a0257fd9bc100e))

### [2.24.3](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.24.2...v2.24.3) (2021-04-29)


### Bug Fixes

* **alarm history:** 更新物联网报警历史类型处理 ([c570c25](https://git.kicad99.com/bfdx/bf8100-web/commit/c570c257b72be9a27de7d6674cd05c506370fd4e))
* **bcxx:**  屏蔽bcxx类指令的状态输出到运行日志 ([14c8de3](https://git.kicad99.com/bfdx/bf8100-web/commit/14c8de35a4c3e6bc9d0e371281b2fd2cef540786))
* **device tree:** 修复树形初始化时，没有正确找到上级节点的问题 ([465c6ec](https://git.kicad99.com/bfdx/bf8100-web/commit/465c6ec0a9327d0e55fc9569657a22f4a184e138))
* **iot:** 修复物联终端解除紧急报警时终端类型判断错误 ([49847a1](https://git.kicad99.com/bfdx/bf8100-web/commit/49847a16f7e7c21df1d445d515f3cc25cc74edaf))
* **iot:** 修改节能灯交流AC开/关指令号，添加红外设置警戒模式开/关指令处理流程 ([9ceed3d](https://git.kicad99.com/bfdx/bf8100-web/commit/9ceed3de9c2d4a0017cfade4eefd4ae3de692342))
* **iot:** 添加节能灯报警命令处理方法 ([6734612](https://git.kicad99.com/bfdx/bf8100-web/commit/67346127bc13ba23dd51a93fa62af9d1570a3448))
* **proto:** 更新db协议 ([394a1f1](https://git.kicad99.com/bfdx/bf8100-web/commit/394a1f1671119a911534129b85b8a1d21c53cbbc))

### [2.24.2](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.24.1...v2.24.2) (2021-04-27)


### Bug Fixes

* **device tree:** 修复树形初始化时，没有正确找到上级节点的问题 ([db22647](https://git.kicad99.com/bfdx/bf8100-web/commit/db226474eb632732b3fd2b92362a250216370161))

### [2.24.1](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.24.0...v2.24.1) (2021-04-26)


### Bug Fixes

* **InputNumber:** 写频功能使用的数字输入框全部添加"stepStrictly"严格精度属性 ([2c34920](https://git.kicad99.com/bfdx/bf8100-web/commit/2c34920fa2777e249cedc45b430a643ab86a6828))
* **TD510SDC:** 修改信令系统单独工作的提醒时间参数范围 ([c52bcca](https://git.kicad99.com/bfdx/bf8100-web/commit/c52bccad9784be55ca42b2b4dcf5b33083afac49))
* **TD510SDC:** 修改信令系统单独工作的提醒时间范围和倒放的退出延时时间参数格式化 ([3726327](https://git.kicad99.com/bfdx/bf8100-web/commit/372632756ac90360b905279182efbd873384eac7))
* **TD510SDC:** 修改扫描/漫游列表信道数上限 ([fb96e04](https://git.kicad99.com/bfdx/bf8100-web/commit/fb96e0473ad99cfec0829908e4a95df034dab798))
* **TD510SDC:** 修改按键定义，屏蔽部分参数定义 ([5606d9f](https://git.kicad99.com/bfdx/bf8100-web/commit/5606d9f10215de56148be67848c747396d5adfec))
* **TD510SDC:** 修改按键定义长、短按候选列表 ([f06eef6](https://git.kicad99.com/bfdx/bf8100-web/commit/f06eef617a3d0c827cf87ed31aae3b1faedce78e))
* **TD510SDC:** 屏蔽电话本功能 ([fabcb27](https://git.kicad99.com/bfdx/bf8100-web/commit/fabcb27957782dad7278ccb0ac433bae38326bb2))
* **TD510SDC:** 限制信令系统数据上限为4个 ([7697f03](https://git.kicad99.com/bfdx/bf8100-web/commit/7697f03318b9b9de7c2b97119d796c3b9bba18ba))
* **TD511SDC:** 修改按键定义，屏蔽部分参数定义 ([7ce0c4a](https://git.kicad99.com/bfdx/bf8100-web/commit/7ce0c4acc073ab7d3916ec9552eb3f5355a5772c))
* **WF:** 修改开机密码输入处理，只能输入数字 ([c530962](https://git.kicad99.com/bfdx/bf8100-web/commit/c5309623107d9ad4ebf4d4ff24a745caa9344332))
* **WF:** 声控等级修改为0(关)-8级 ([d3a0651](https://git.kicad99.com/bfdx/bf8100-web/commit/d3a065165e75cb344af1480a52aba4d4e9c06e5d))

## [2.24.0](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.23.2...v2.24.0) (2021-04-23)


### Features

* **IOT:** 添加温/湿度探测器终端，及相关指令处理功能 ([a7af022](https://git.kicad99.com/bfdx/bf8100-web/commit/a7af022d6844e9ed3ba2371f95922c83aa7ff243))
* **IOT:** 添加节能灯终端，及相关指令处理(当前只有心跳) ([cecc8e2](https://git.kicad99.com/bfdx/bf8100-web/commit/cecc8e2cadbd5ec2b4fd809c6bfbc83c5b2bac19))

### [2.23.2](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.23.1...v2.23.2) (2021-04-23)


### Bug Fixes

* **i18n:** 修复语言包初始化缓慢，导致登录成功后缓存的语言信息错误 ([7c7f48f](https://git.kicad99.com/bfdx/bf8100-web/commit/7c7f48f7445db1cbe56673fd613ff0f300ad3f8d))

### [2.23.1](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.23.0...v2.23.1) (2021-04-22)


### Bug Fixes

* **BF-TD800(SDC):** 修复新增的机型码在写入时检验无法通过的错误 ([99bafa4](https://git.kicad99.com/bfdx/bf8100-web/commit/99bafa43354c19c4f00e4381ee39a3ed27150671))

## [2.23.0](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.22.2...v2.23.0) (2021-04-22)


### Features

* **BF-TD510(SDC):** 修改510SDC界面显示，屏蔽菜单项及相关配置 ([e49214a](https://git.kicad99.com/bfdx/bf8100-web/commit/e49214afb7cb47a0ce184c79dad2370cc1b7d48e))
* **BF-TD510(SDC):** 修改TD081000机型写入数据时机型校验判断逻辑，添加识别TD081100机型 ([2b744f4](https://git.kicad99.com/bfdx/bf8100-web/commit/2b744f43c051468c72389ad1a9a01b78d1ba3e83))
* **BF-TD510(SDC):** 添加新的机型写频入口组件 ([6e4ff76](https://git.kicad99.com/bfdx/bf8100-web/commit/6e4ff769316ceeaab5b052b4f140058edae25821))
* **BF-TD510(SDC):** 终端管理添加510SDC机型的区域配置信息 ([dca9c46](https://git.kicad99.com/bfdx/bf8100-web/commit/dca9c466e9ad608cc4fb2d1e86439b2e8142835d))
* **WF:** 修改机型信息，BF-TD810改为BF-TD800(SDC)，并添加一个新的机型码 ([a7d87ea](https://git.kicad99.com/bfdx/bf8100-web/commit/a7d87ea1667f99f60dbdfcc1d651e781554b4d26))

### [2.22.2](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.22.1...v2.22.2) (2021-04-21)


### Bug Fixes

* update scc style ([138988b](https://git.kicad99.com/bfdx/bf8100-web/commit/138988b21e2513ff6080c57526c279687df8f867))

### [2.22.1](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.22.0...v2.22.1) (2021-03-26)


### Bug Fixes

* **datatables:** 修复数据表格高度计算单位(%)的使用，导致在最大化后，数据渲染不完全问题。修改为"vh" ([414b3bf](https://git.kicad99.com/bfdx/bf8100-web/commit/414b3bfdfc6d57db58cd4c87e20687eee47d9198))

## [2.22.0](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.21.3...v2.22.0) (2021-03-10)


### Features

* **iot control:** 添加物联网功能开关控制逻辑 ([fc4a01b](https://git.kicad99.com/bfdx/bf8100-web/commit/fc4a01ba958648b1b17af6fd65ecc90ff4acf951))

### [2.21.3](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.21.2...v2.21.3) (2021-02-19)


### Bug Fixes

* **TM8250SDC:** 修改写频相关参数配置 ([d44ac54](https://git.kicad99.com/bfdx/bf8100-web/commit/d44ac54fc6d82d7dfc73aebc95d951812eb051dd))
* **TM8250SDC:** 更新写频协议 ([e8727bf](https://git.kicad99.com/bfdx/bf8100-web/commit/e8727bf20dcc1a0b90d577877086ba04716a2593))

### [2.21.2](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.21.1...v2.21.2) (2021-02-19)


### Bug Fixes

* **ci:** update .gitlab-ci.yml ([e5550d9](https://git.kicad99.com/bfdx/bf8100-web/commit/e5550d99a92955698fce1c29b1b08d1c889062a7))

## [2.21.0](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.20.46...v2.21.0) (2021-02-19)


### Features

* use const for number ([0a612d1](https://git.kicad99.com/bfdx/bf8100-web/commit/0a612d1df55f655aa5715140aa99efd207b36b71))
* **CmdAgent Channel config:** impl dbDevice data edit.add cmdAgent channel config ([04c13b3](https://git.kicad99.com/bfdx/bf8100-web/commit/04c13b3236ea127edfea32358a1569c08ba9126d))
* **iot:** 添加物卡各类指令处理功能 ([47fe83e](https://git.kicad99.com/bfdx/bf8100-web/commit/47fe83e1217be56606322864a0009af45a200853))
* add iot dec his page ([aa875f5](https://git.kicad99.com/bfdx/bf8100-web/commit/aa875f505efd439f929e65b3b863928f6657ccff))
* add iot device history icon ([b6fad7d](https://git.kicad99.com/bfdx/bf8100-web/commit/b6fad7d56c8c35069a5caec2dea544364ce861ad))
* add lang transfer ([ae24baf](https://git.kicad99.com/bfdx/bf8100-web/commit/ae24baf8ce1baf600da65c4814b0fadd246b2f73))
* add lang transfer ([a8a4b21](https://git.kicad99.com/bfdx/bf8100-web/commit/a8a4b21f8af771f10a13fc9bbe1a69444d6a19aa))
* compile proto with oit dev ([1379b4d](https://git.kicad99.com/bfdx/bf8100-web/commit/1379b4d6c8ef0ef37aa3f3b0308c17387c4aac72))
* impl query iot device history ([6d4b8b4](https://git.kicad99.com/bfdx/bf8100-web/commit/6d4b8b426a480d172898ac804d31453205dc7dda))
* lint format ([e561709](https://git.kicad99.com/bfdx/bf8100-web/commit/e5617095b82787a36599c06829b46aa4af346126))
* **iot device:** 修改物联网终端marker popup内容 ([07643a5](https://git.kicad99.com/bfdx/bf8100-web/commit/07643a549ddf390d8b11a2e7d817267174441b17))
* **iot device:** 添加物联网终端地图标记显示控制设置 ([713f4e7](https://git.kicad99.com/bfdx/bf8100-web/commit/713f4e764753f79f7a8976a1bd98f58e5bb6b592))
* **iot device:** 终端管理添加物联巡查终端类型，DMRID值为5个字节的16进制ID ([09cb2d3](https://git.kicad99.com/bfdx/bf8100-web/commit/09cb2d3316040d3dad9d586bb38ca74807b4c404))
* **line-point:** point type add BaseStation Patrol Point ([d5c2050](https://git.kicad99.com/bfdx/bf8100-web/commit/d5c20504a44de418b7d882f827cc2841436f5725))


### Bug Fixes

* **agentDev:** change eslint debugger ([375ad53](https://git.kicad99.com/bfdx/bf8100-web/commit/375ad536207132469565687fbab45b0ea70c0157))
* **agentDev:** del debugger ([43145bb](https://git.kicad99.com/bfdx/bf8100-web/commit/43145bbee24e7d4f12330cc227d232cf03ccb7d4))
* **agentDev:** impl sync other client data for CmdAgent PC-Dev listen groups ([852ec67](https://git.kicad99.com/bfdx/bf8100-web/commit/852ec67475a4b31db03646cbb44d044066249c55))
* **agentDev:** listen group show noPermission unit ([17416ec](https://git.kicad99.com/bfdx/bf8100-web/commit/17416ec960d4926d4f269fd9d6157a7d1f452b8e))
* **agentDev:** query no permission unit data ([2e00c80](https://git.kicad99.com/bfdx/bf8100-web/commit/2e00c8028078b9f1cb74638f71acdfcf326eb410))
* **alarm history:** 修改解析历史数据功能，添加处理物联终端逻辑 ([ea3041a](https://git.kicad99.com/bfdx/bf8100-web/commit/ea3041a88c34cb58f051a44e7c5addfd16a7f679))
* **alarm history:** 终端条件添加物联终端选项 ([d09dba6](https://git.kicad99.com/bfdx/bf8100-web/commit/d09dba6ba9177cb5ef93eac0c34df4d177e05f16))
* **cache:** 请求静态json文件时，添加时间参数，以便浏览器不缓存资源 ([538b32a](https://git.kicad99.com/bfdx/bf8100-web/commit/538b32ab7f3e7ccc7f1625b8bf037aebfc10d51e))
* **ci:** debug semantic-release ([15f2659](https://git.kicad99.com/bfdx/bf8100-web/commit/15f2659ceb5d0e16633b38f56a254fc583f80449))
* **ci:** debug semantic-release ([2c21022](https://git.kicad99.com/bfdx/bf8100-web/commit/2c210222503d901c2beaa621dcd48a14126f4e4e))
* **ci:** semantic-release add "--dry-run" argv ([0dbbfe1](https://git.kicad99.com/bfdx/bf8100-web/commit/0dbbfe10d79ee916599431bfcf31b476a9fefcad))
* **ci:** update .gitlab-ci.yml ([dc3c818](https://git.kicad99.com/bfdx/bf8100-web/commit/dc3c818486f270c3ddd2ad6c5dfa757a30b9ecf1))
* **ci:** update .gitlab-ci.yml ([dea47e0](https://git.kicad99.com/bfdx/bf8100-web/commit/dea47e052696cefd1967f42cfc708a127cb568d2))
* **ci:** update .gitlab-ci.yml ([dbc4277](https://git.kicad99.com/bfdx/bf8100-web/commit/dbc427720f157f2353e2873760cbdcd811896e93))
* **ci:** update .gitlab-ci.yml ([0821825](https://git.kicad99.com/bfdx/bf8100-web/commit/082182536e191b76f56a74c9ffab2ef81c2705c3))
* **ci:** update .gitlab-ci.yml ([c349333](https://git.kicad99.com/bfdx/bf8100-web/commit/c34933318ba53ad173df72c905b9335d42176934))
* **ci:** update .gitlab-ci.yml ([42b9d11](https://git.kicad99.com/bfdx/bf8100-web/commit/42b9d11f2d2a1e91ddee2b571956d83bf9445b6d))
* **ci:** update ci release job ([3a5c19a](https://git.kicad99.com/bfdx/bf8100-web/commit/3a5c19ae9789e2f98e74aaf580952c536925e25e))
* **ci:** update semantic-release ([ce87943](https://git.kicad99.com/bfdx/bf8100-web/commit/ce87943b8e55189e5708925b3d9fd23dee9adf03))
* **ci:** update semantic-release ([4c92b2b](https://git.kicad99.com/bfdx/bf8100-web/commit/4c92b2b3252ccef2db8051de98ea2f03f1c7161c))
* **ci:** update semantic-release ([7d84dbb](https://git.kicad99.com/bfdx/bf8100-web/commit/7d84dbbd3c73a593624306f23ae8f80da1d4652d))
* **ci:** update semantic-release ([d4de7ef](https://git.kicad99.com/bfdx/bf8100-web/commit/d4de7efa075a587eac0916f62d9e62928a4a9686))
* **ci:** update semantic-release ([34f945c](https://git.kicad99.com/bfdx/bf8100-web/commit/34f945c643cd5caeb85984bad63f351615ad33f6))
* **ci:** update semantic-release job ([3ab94e0](https://git.kicad99.com/bfdx/bf8100-web/commit/3ab94e050e27d507abc3a55c12d4ed0cb491fcda))
* **ci:** update semantic-release job ([fe59eac](https://git.kicad99.com/bfdx/bf8100-web/commit/fe59eacce86ea7cf1b7a6a2981b5095b75ff0a83))
* **device:** merge master into current branch ([c9f80a3](https://git.kicad99.com/bfdx/bf8100-web/commit/c9f80a3d350161fc8a6325357262f0c2be9b3f6e))
* **device:** 修复终端信道管理，当前已经选择的接收组数据没有同步单位的dmrId问题 ([909a3b0](https://git.kicad99.com/bfdx/bf8100-web/commit/909a3b0817e0faf21cce8da93edd6d923be77bd7))
* **device:** 修改指挥坐席终端拷贝信道时只拷贝1信道 ([4aca1dd](https://git.kicad99.com/bfdx/bf8100-web/commit/4aca1ddd1067e003c57d44cbb0530b6c7f5974ef))
* **Emergency:** 紧急报警界面对于IOT设备处理。隐藏用户，devSelfId改为devName ([dda8ce4](https://git.kicad99.com/bfdx/bf8100-web/commit/dda8ce472440ad5f5c106d33ad19821b8ca9e5d4))
* **iconfont:** 更新系统图标库 ([e30ddf1](https://git.kicad99.com/bfdx/bf8100-web/commit/e30ddf1d2cbfe3cdfcd517d9c63cf21e768ccd18))
* **iconfont:** 更新系统图标库 ([3996ce1](https://git.kicad99.com/bfdx/bf8100-web/commit/3996ce1acd41c566da7d45668af5607d6238bd93))
* **iot:** 修复更新物联终端时，没有同步更新地图marker数据问题 ([20d0497](https://git.kicad99.com/bfdx/bf8100-web/commit/20d0497b3624ec2f8f1bce99028fb0cac1964ccf))
* **iot:** 修改iot命令数据逻辑，添加最后数据更新和巡查点状态更新功能 ([7cff5c4](https://git.kicad99.com/bfdx/bf8100-web/commit/7cff5c4335a5f700d71fe9c0b59a82ee918a92e9))
* **iot:** 修改人员卡指令更新巡查点marker状态变化功能 ([5cd8358](https://git.kicad99.com/bfdx/bf8100-web/commit/5cd83588dc8ce047be632404d581ca0ec3420527))
* **iot:** 修改物联终端历史指令类型和数据类型对象映射 ([331f635](https://git.kicad99.com/bfdx/bf8100-web/commit/331f6352f888a247dbc07da63e24ca5ce6b41c78))
* **iot:** 完善工牌巡查打卡通知、日志等功能 ([998b6c3](https://git.kicad99.com/bfdx/bf8100-web/commit/998b6c3b44e11fbef5a61ad978c7fe38ed2f2e2d))
* **iot:** 更新iot相关协议 ([6e591d2](https://git.kicad99.com/bfdx/bf8100-web/commit/6e591d296ac0101fb43dae0fe1e239b359efbeb6))
* **iot:** 更新iot相关协议 ([6e8253f](https://git.kicad99.com/bfdx/bf8100-web/commit/6e8253f55c5dc8d5139976e1856ead716bb9394d))
* **iot:** 添加iot模块处理iot各类命令 ([d9e779e](https://git.kicad99.com/bfdx/bf8100-web/commit/d9e779e9d1f2fd29fb877d7f904f11a282d31d97))
* **iot:** 添加工牌紧急报警处理逻辑 ([674bd24](https://git.kicad99.com/bfdx/bf8100-web/commit/674bd24d2a2d39f4ec3ce7580c2fa0c3fa8035e6))
* **iot:** 添加烟感的指令处理方法，修改marker popup内容(添加最后指令项) ([05cdf9d](https://git.kicad99.com/bfdx/bf8100-web/commit/05cdf9df9456bbd81d72504ab08c843279725f00))
* **iot:** 添加物卡指令处理入口方法 ([554682c](https://git.kicad99.com/bfdx/bf8100-web/commit/554682c2e5ee19fe034e2162d0e38c6ac6c35890))
* **iot:** 物联终端24小时无数据，则显示橙色图标； ([32629e5](https://git.kicad99.com/bfdx/bf8100-web/commit/32629e594351c759a0cc1ffa438133236a8d959b))
* **iot device:** 创建物联网终端地图marker ([78e9ea4](https://git.kicad99.com/bfdx/bf8100-web/commit/78e9ea4e13931e5f6640c5e05fdf9839e42dab08))
* **iot device:** 添加物联网终端数据管理逻辑 ([4ad96b6](https://git.kicad99.com/bfdx/bf8100-web/commit/4ad96b6983592a0078275b833ab27fb1f3317157))
* **iot device:** 添加物联网终端管理组件 ([c0c612f](https://git.kicad99.com/bfdx/bf8100-web/commit/c0c612fabdb7e601d4f7e119133ad14121d6478b))
* **package:** upgrade package dependencies ([7229a39](https://git.kicad99.com/bfdx/bf8100-web/commit/7229a39a9749ccd4eb20d7b794e5778caee00cff))
* **package:** upgrade version to 2.21.1 ([4988cdf](https://git.kicad99.com/bfdx/bf8100-web/commit/4988cdf0647a176f809c4edc95c92b24c11c1f65))
* fix bug at iot dev form ([389c44f](https://git.kicad99.com/bfdx/bf8100-web/commit/389c44f4d93c4571c4de5607be716042c41900ca))
* impl query iot device history ([5840292](https://git.kicad99.com/bfdx/bf8100-web/commit/58402923b8467b77a53bf9f044f4595d3bafc65d))
* impl query iot device last info ([de862c9](https://git.kicad99.com/bfdx/bf8100-web/commit/de862c9d014d808aea50b978a6b0595b5382cad4))
* impl query iot device last info ([03fcdd5](https://git.kicad99.com/bfdx/bf8100-web/commit/03fcdd5b3b33005a63009d16c04492dbc2c9babc))
* **proto:** 更新协议，添加iot物联网 ([29797ac](https://git.kicad99.com/bfdx/bf8100-web/commit/29797ac9bf73375b6ad7adf4b88fb11025678325))

## [2.21.0](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.20.46...v2.21.0) (2021-02-19)


### Features

* use const for number ([0a612d1](https://git.kicad99.com/bfdx/bf8100-web/commit/0a612d1df55f655aa5715140aa99efd207b36b71))
* **CmdAgent Channel config:** impl dbDevice data edit.add cmdAgent channel config ([04c13b3](https://git.kicad99.com/bfdx/bf8100-web/commit/04c13b3236ea127edfea32358a1569c08ba9126d))
* **iot:** 添加物卡各类指令处理功能 ([47fe83e](https://git.kicad99.com/bfdx/bf8100-web/commit/47fe83e1217be56606322864a0009af45a200853))
* add iot dec his page ([aa875f5](https://git.kicad99.com/bfdx/bf8100-web/commit/aa875f505efd439f929e65b3b863928f6657ccff))
* add iot device history icon ([b6fad7d](https://git.kicad99.com/bfdx/bf8100-web/commit/b6fad7d56c8c35069a5caec2dea544364ce861ad))
* add lang transfer ([ae24baf](https://git.kicad99.com/bfdx/bf8100-web/commit/ae24baf8ce1baf600da65c4814b0fadd246b2f73))
* add lang transfer ([a8a4b21](https://git.kicad99.com/bfdx/bf8100-web/commit/a8a4b21f8af771f10a13fc9bbe1a69444d6a19aa))
* compile proto with oit dev ([1379b4d](https://git.kicad99.com/bfdx/bf8100-web/commit/1379b4d6c8ef0ef37aa3f3b0308c17387c4aac72))
* impl query iot device history ([6d4b8b4](https://git.kicad99.com/bfdx/bf8100-web/commit/6d4b8b426a480d172898ac804d31453205dc7dda))
* lint format ([e561709](https://git.kicad99.com/bfdx/bf8100-web/commit/e5617095b82787a36599c06829b46aa4af346126))
* **iot device:** 修改物联网终端marker popup内容 ([07643a5](https://git.kicad99.com/bfdx/bf8100-web/commit/07643a549ddf390d8b11a2e7d817267174441b17))
* **iot device:** 添加物联网终端地图标记显示控制设置 ([713f4e7](https://git.kicad99.com/bfdx/bf8100-web/commit/713f4e764753f79f7a8976a1bd98f58e5bb6b592))
* **iot device:** 终端管理添加物联巡查终端类型，DMRID值为5个字节的16进制ID ([09cb2d3](https://git.kicad99.com/bfdx/bf8100-web/commit/09cb2d3316040d3dad9d586bb38ca74807b4c404))
* **line-point:** point type add BaseStation Patrol Point ([d5c2050](https://git.kicad99.com/bfdx/bf8100-web/commit/d5c20504a44de418b7d882f827cc2841436f5725))


### Bug Fixes

* **agentDev:** change eslint debugger ([375ad53](https://git.kicad99.com/bfdx/bf8100-web/commit/375ad536207132469565687fbab45b0ea70c0157))
* **agentDev:** del debugger ([43145bb](https://git.kicad99.com/bfdx/bf8100-web/commit/43145bbee24e7d4f12330cc227d232cf03ccb7d4))
* **agentDev:** impl sync other client data for CmdAgent PC-Dev listen groups ([852ec67](https://git.kicad99.com/bfdx/bf8100-web/commit/852ec67475a4b31db03646cbb44d044066249c55))
* **agentDev:** listen group show noPermission unit ([17416ec](https://git.kicad99.com/bfdx/bf8100-web/commit/17416ec960d4926d4f269fd9d6157a7d1f452b8e))
* **agentDev:** query no permission unit data ([2e00c80](https://git.kicad99.com/bfdx/bf8100-web/commit/2e00c8028078b9f1cb74638f71acdfcf326eb410))
* **alarm history:** 修改解析历史数据功能，添加处理物联终端逻辑 ([ea3041a](https://git.kicad99.com/bfdx/bf8100-web/commit/ea3041a88c34cb58f051a44e7c5addfd16a7f679))
* **alarm history:** 终端条件添加物联终端选项 ([d09dba6](https://git.kicad99.com/bfdx/bf8100-web/commit/d09dba6ba9177cb5ef93eac0c34df4d177e05f16))
* **cache:** 请求静态json文件时，添加时间参数，以便浏览器不缓存资源 ([538b32a](https://git.kicad99.com/bfdx/bf8100-web/commit/538b32ab7f3e7ccc7f1625b8bf037aebfc10d51e))
* **ci:** debug semantic-release ([15f2659](https://git.kicad99.com/bfdx/bf8100-web/commit/15f2659ceb5d0e16633b38f56a254fc583f80449))
* **ci:** debug semantic-release ([2c21022](https://git.kicad99.com/bfdx/bf8100-web/commit/2c210222503d901c2beaa621dcd48a14126f4e4e))
* **ci:** semantic-release add "--dry-run" argv ([0dbbfe1](https://git.kicad99.com/bfdx/bf8100-web/commit/0dbbfe10d79ee916599431bfcf31b476a9fefcad))
* **ci:** update .gitlab-ci.yml ([dc3c818](https://git.kicad99.com/bfdx/bf8100-web/commit/dc3c818486f270c3ddd2ad6c5dfa757a30b9ecf1))
* **ci:** update .gitlab-ci.yml ([dea47e0](https://git.kicad99.com/bfdx/bf8100-web/commit/dea47e052696cefd1967f42cfc708a127cb568d2))
* **ci:** update .gitlab-ci.yml ([dbc4277](https://git.kicad99.com/bfdx/bf8100-web/commit/dbc427720f157f2353e2873760cbdcd811896e93))
* **ci:** update .gitlab-ci.yml ([0821825](https://git.kicad99.com/bfdx/bf8100-web/commit/082182536e191b76f56a74c9ffab2ef81c2705c3))
* **ci:** update .gitlab-ci.yml ([c349333](https://git.kicad99.com/bfdx/bf8100-web/commit/c34933318ba53ad173df72c905b9335d42176934))
* **ci:** update .gitlab-ci.yml ([42b9d11](https://git.kicad99.com/bfdx/bf8100-web/commit/42b9d11f2d2a1e91ddee2b571956d83bf9445b6d))
* **ci:** update ci release job ([3a5c19a](https://git.kicad99.com/bfdx/bf8100-web/commit/3a5c19ae9789e2f98e74aaf580952c536925e25e))
* **ci:** update semantic-release ([ce87943](https://git.kicad99.com/bfdx/bf8100-web/commit/ce87943b8e55189e5708925b3d9fd23dee9adf03))
* **ci:** update semantic-release ([4c92b2b](https://git.kicad99.com/bfdx/bf8100-web/commit/4c92b2b3252ccef2db8051de98ea2f03f1c7161c))
* **ci:** update semantic-release ([7d84dbb](https://git.kicad99.com/bfdx/bf8100-web/commit/7d84dbbd3c73a593624306f23ae8f80da1d4652d))
* **ci:** update semantic-release ([d4de7ef](https://git.kicad99.com/bfdx/bf8100-web/commit/d4de7efa075a587eac0916f62d9e62928a4a9686))
* **ci:** update semantic-release ([34f945c](https://git.kicad99.com/bfdx/bf8100-web/commit/34f945c643cd5caeb85984bad63f351615ad33f6))
* **ci:** update semantic-release job ([3ab94e0](https://git.kicad99.com/bfdx/bf8100-web/commit/3ab94e050e27d507abc3a55c12d4ed0cb491fcda))
* **ci:** update semantic-release job ([fe59eac](https://git.kicad99.com/bfdx/bf8100-web/commit/fe59eacce86ea7cf1b7a6a2981b5095b75ff0a83))
* **device:** merge master into current branch ([c9f80a3](https://git.kicad99.com/bfdx/bf8100-web/commit/c9f80a3d350161fc8a6325357262f0c2be9b3f6e))
* **device:** 修复终端信道管理，当前已经选择的接收组数据没有同步单位的dmrId问题 ([909a3b0](https://git.kicad99.com/bfdx/bf8100-web/commit/909a3b0817e0faf21cce8da93edd6d923be77bd7))
* **device:** 修改指挥坐席终端拷贝信道时只拷贝1信道 ([4aca1dd](https://git.kicad99.com/bfdx/bf8100-web/commit/4aca1ddd1067e003c57d44cbb0530b6c7f5974ef))
* **Emergency:** 紧急报警界面对于IOT设备处理。隐藏用户，devSelfId改为devName ([dda8ce4](https://git.kicad99.com/bfdx/bf8100-web/commit/dda8ce472440ad5f5c106d33ad19821b8ca9e5d4))
* **iconfont:** 更新系统图标库 ([e30ddf1](https://git.kicad99.com/bfdx/bf8100-web/commit/e30ddf1d2cbfe3cdfcd517d9c63cf21e768ccd18))
* **iconfont:** 更新系统图标库 ([3996ce1](https://git.kicad99.com/bfdx/bf8100-web/commit/3996ce1acd41c566da7d45668af5607d6238bd93))
* **iot:** 修复更新物联终端时，没有同步更新地图marker数据问题 ([20d0497](https://git.kicad99.com/bfdx/bf8100-web/commit/20d0497b3624ec2f8f1bce99028fb0cac1964ccf))
* **iot:** 修改iot命令数据逻辑，添加最后数据更新和巡查点状态更新功能 ([7cff5c4](https://git.kicad99.com/bfdx/bf8100-web/commit/7cff5c4335a5f700d71fe9c0b59a82ee918a92e9))
* **iot:** 修改人员卡指令更新巡查点marker状态变化功能 ([5cd8358](https://git.kicad99.com/bfdx/bf8100-web/commit/5cd83588dc8ce047be632404d581ca0ec3420527))
* **iot:** 修改物联终端历史指令类型和数据类型对象映射 ([331f635](https://git.kicad99.com/bfdx/bf8100-web/commit/331f6352f888a247dbc07da63e24ca5ce6b41c78))
* **iot:** 完善工牌巡查打卡通知、日志等功能 ([998b6c3](https://git.kicad99.com/bfdx/bf8100-web/commit/998b6c3b44e11fbef5a61ad978c7fe38ed2f2e2d))
* **iot:** 更新iot相关协议 ([6e591d2](https://git.kicad99.com/bfdx/bf8100-web/commit/6e591d296ac0101fb43dae0fe1e239b359efbeb6))
* **iot:** 更新iot相关协议 ([6e8253f](https://git.kicad99.com/bfdx/bf8100-web/commit/6e8253f55c5dc8d5139976e1856ead716bb9394d))
* **iot:** 添加iot模块处理iot各类命令 ([d9e779e](https://git.kicad99.com/bfdx/bf8100-web/commit/d9e779e9d1f2fd29fb877d7f904f11a282d31d97))
* **iot:** 添加工牌紧急报警处理逻辑 ([674bd24](https://git.kicad99.com/bfdx/bf8100-web/commit/674bd24d2a2d39f4ec3ce7580c2fa0c3fa8035e6))
* **iot:** 添加烟感的指令处理方法，修改marker popup内容(添加最后指令项) ([05cdf9d](https://git.kicad99.com/bfdx/bf8100-web/commit/05cdf9df9456bbd81d72504ab08c843279725f00))
* **iot:** 添加物卡指令处理入口方法 ([554682c](https://git.kicad99.com/bfdx/bf8100-web/commit/554682c2e5ee19fe034e2162d0e38c6ac6c35890))
* **iot:** 物联终端24小时无数据，则显示橙色图标； ([32629e5](https://git.kicad99.com/bfdx/bf8100-web/commit/32629e594351c759a0cc1ffa438133236a8d959b))
* **iot device:** 创建物联网终端地图marker ([78e9ea4](https://git.kicad99.com/bfdx/bf8100-web/commit/78e9ea4e13931e5f6640c5e05fdf9839e42dab08))
* **iot device:** 添加物联网终端数据管理逻辑 ([4ad96b6](https://git.kicad99.com/bfdx/bf8100-web/commit/4ad96b6983592a0078275b833ab27fb1f3317157))
* **iot device:** 添加物联网终端管理组件 ([c0c612f](https://git.kicad99.com/bfdx/bf8100-web/commit/c0c612fabdb7e601d4f7e119133ad14121d6478b))
* **package:** upgrade package dependencies ([7229a39](https://git.kicad99.com/bfdx/bf8100-web/commit/7229a39a9749ccd4eb20d7b794e5778caee00cff))
* **package:** upgrade version to 2.21.1 ([4988cdf](https://git.kicad99.com/bfdx/bf8100-web/commit/4988cdf0647a176f809c4edc95c92b24c11c1f65))
* fix bug at iot dev form ([389c44f](https://git.kicad99.com/bfdx/bf8100-web/commit/389c44f4d93c4571c4de5607be716042c41900ca))
* impl query iot device history ([5840292](https://git.kicad99.com/bfdx/bf8100-web/commit/58402923b8467b77a53bf9f044f4595d3bafc65d))
* impl query iot device last info ([de862c9](https://git.kicad99.com/bfdx/bf8100-web/commit/de862c9d014d808aea50b978a6b0595b5382cad4))
* impl query iot device last info ([03fcdd5](https://git.kicad99.com/bfdx/bf8100-web/commit/03fcdd5b3b33005a63009d16c04492dbc2c9babc))
* **proto:** 更新协议，添加iot物联网 ([29797ac](https://git.kicad99.com/bfdx/bf8100-web/commit/29797ac9bf73375b6ad7adf4b88fb11025678325))

## [2.21.0](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.20.46...v2.21.0) (2021-02-19)


### Features

* use const for number ([0a612d1](https://git.kicad99.com/bfdx/bf8100-web/commit/0a612d1df55f655aa5715140aa99efd207b36b71))
* **CmdAgent Channel config:** impl dbDevice data edit.add cmdAgent channel config ([04c13b3](https://git.kicad99.com/bfdx/bf8100-web/commit/04c13b3236ea127edfea32358a1569c08ba9126d))
* **iot:** 添加物卡各类指令处理功能 ([47fe83e](https://git.kicad99.com/bfdx/bf8100-web/commit/47fe83e1217be56606322864a0009af45a200853))
* add iot dec his page ([aa875f5](https://git.kicad99.com/bfdx/bf8100-web/commit/aa875f505efd439f929e65b3b863928f6657ccff))
* add iot device history icon ([b6fad7d](https://git.kicad99.com/bfdx/bf8100-web/commit/b6fad7d56c8c35069a5caec2dea544364ce861ad))
* add lang transfer ([ae24baf](https://git.kicad99.com/bfdx/bf8100-web/commit/ae24baf8ce1baf600da65c4814b0fadd246b2f73))
* add lang transfer ([a8a4b21](https://git.kicad99.com/bfdx/bf8100-web/commit/a8a4b21f8af771f10a13fc9bbe1a69444d6a19aa))
* compile proto with oit dev ([1379b4d](https://git.kicad99.com/bfdx/bf8100-web/commit/1379b4d6c8ef0ef37aa3f3b0308c17387c4aac72))
* impl query iot device history ([6d4b8b4](https://git.kicad99.com/bfdx/bf8100-web/commit/6d4b8b426a480d172898ac804d31453205dc7dda))
* lint format ([e561709](https://git.kicad99.com/bfdx/bf8100-web/commit/e5617095b82787a36599c06829b46aa4af346126))
* **iot device:** 修改物联网终端marker popup内容 ([07643a5](https://git.kicad99.com/bfdx/bf8100-web/commit/07643a549ddf390d8b11a2e7d817267174441b17))
* **iot device:** 添加物联网终端地图标记显示控制设置 ([713f4e7](https://git.kicad99.com/bfdx/bf8100-web/commit/713f4e764753f79f7a8976a1bd98f58e5bb6b592))
* **iot device:** 终端管理添加物联巡查终端类型，DMRID值为5个字节的16进制ID ([09cb2d3](https://git.kicad99.com/bfdx/bf8100-web/commit/09cb2d3316040d3dad9d586bb38ca74807b4c404))
* **line-point:** point type add BaseStation Patrol Point ([d5c2050](https://git.kicad99.com/bfdx/bf8100-web/commit/d5c20504a44de418b7d882f827cc2841436f5725))


### Bug Fixes

* **agentDev:** change eslint debugger ([375ad53](https://git.kicad99.com/bfdx/bf8100-web/commit/375ad536207132469565687fbab45b0ea70c0157))
* **agentDev:** del debugger ([43145bb](https://git.kicad99.com/bfdx/bf8100-web/commit/43145bbee24e7d4f12330cc227d232cf03ccb7d4))
* **agentDev:** impl sync other client data for CmdAgent PC-Dev listen groups ([852ec67](https://git.kicad99.com/bfdx/bf8100-web/commit/852ec67475a4b31db03646cbb44d044066249c55))
* **agentDev:** listen group show noPermission unit ([17416ec](https://git.kicad99.com/bfdx/bf8100-web/commit/17416ec960d4926d4f269fd9d6157a7d1f452b8e))
* **agentDev:** query no permission unit data ([2e00c80](https://git.kicad99.com/bfdx/bf8100-web/commit/2e00c8028078b9f1cb74638f71acdfcf326eb410))
* **alarm history:** 修改解析历史数据功能，添加处理物联终端逻辑 ([ea3041a](https://git.kicad99.com/bfdx/bf8100-web/commit/ea3041a88c34cb58f051a44e7c5addfd16a7f679))
* **alarm history:** 终端条件添加物联终端选项 ([d09dba6](https://git.kicad99.com/bfdx/bf8100-web/commit/d09dba6ba9177cb5ef93eac0c34df4d177e05f16))
* **cache:** 请求静态json文件时，添加时间参数，以便浏览器不缓存资源 ([538b32a](https://git.kicad99.com/bfdx/bf8100-web/commit/538b32ab7f3e7ccc7f1625b8bf037aebfc10d51e))
* **ci:** debug semantic-release ([15f2659](https://git.kicad99.com/bfdx/bf8100-web/commit/15f2659ceb5d0e16633b38f56a254fc583f80449))
* **ci:** debug semantic-release ([2c21022](https://git.kicad99.com/bfdx/bf8100-web/commit/2c210222503d901c2beaa621dcd48a14126f4e4e))
* **ci:** semantic-release add "--dry-run" argv ([0dbbfe1](https://git.kicad99.com/bfdx/bf8100-web/commit/0dbbfe10d79ee916599431bfcf31b476a9fefcad))
* **ci:** update .gitlab-ci.yml ([dbc4277](https://git.kicad99.com/bfdx/bf8100-web/commit/dbc427720f157f2353e2873760cbdcd811896e93))
* **ci:** update .gitlab-ci.yml ([0821825](https://git.kicad99.com/bfdx/bf8100-web/commit/082182536e191b76f56a74c9ffab2ef81c2705c3))
* **ci:** update .gitlab-ci.yml ([c349333](https://git.kicad99.com/bfdx/bf8100-web/commit/c34933318ba53ad173df72c905b9335d42176934))
* **ci:** update .gitlab-ci.yml ([42b9d11](https://git.kicad99.com/bfdx/bf8100-web/commit/42b9d11f2d2a1e91ddee2b571956d83bf9445b6d))
* **ci:** update ci release job ([3a5c19a](https://git.kicad99.com/bfdx/bf8100-web/commit/3a5c19ae9789e2f98e74aaf580952c536925e25e))
* **ci:** update semantic-release ([ce87943](https://git.kicad99.com/bfdx/bf8100-web/commit/ce87943b8e55189e5708925b3d9fd23dee9adf03))
* **ci:** update semantic-release ([4c92b2b](https://git.kicad99.com/bfdx/bf8100-web/commit/4c92b2b3252ccef2db8051de98ea2f03f1c7161c))
* **ci:** update semantic-release ([7d84dbb](https://git.kicad99.com/bfdx/bf8100-web/commit/7d84dbbd3c73a593624306f23ae8f80da1d4652d))
* **ci:** update semantic-release ([d4de7ef](https://git.kicad99.com/bfdx/bf8100-web/commit/d4de7efa075a587eac0916f62d9e62928a4a9686))
* **ci:** update semantic-release ([34f945c](https://git.kicad99.com/bfdx/bf8100-web/commit/34f945c643cd5caeb85984bad63f351615ad33f6))
* **ci:** update semantic-release job ([3ab94e0](https://git.kicad99.com/bfdx/bf8100-web/commit/3ab94e050e27d507abc3a55c12d4ed0cb491fcda))
* **ci:** update semantic-release job ([fe59eac](https://git.kicad99.com/bfdx/bf8100-web/commit/fe59eacce86ea7cf1b7a6a2981b5095b75ff0a83))
* **device:** merge master into current branch ([c9f80a3](https://git.kicad99.com/bfdx/bf8100-web/commit/c9f80a3d350161fc8a6325357262f0c2be9b3f6e))
* **device:** 修复终端信道管理，当前已经选择的接收组数据没有同步单位的dmrId问题 ([909a3b0](https://git.kicad99.com/bfdx/bf8100-web/commit/909a3b0817e0faf21cce8da93edd6d923be77bd7))
* **device:** 修改指挥坐席终端拷贝信道时只拷贝1信道 ([4aca1dd](https://git.kicad99.com/bfdx/bf8100-web/commit/4aca1ddd1067e003c57d44cbb0530b6c7f5974ef))
* **Emergency:** 紧急报警界面对于IOT设备处理。隐藏用户，devSelfId改为devName ([dda8ce4](https://git.kicad99.com/bfdx/bf8100-web/commit/dda8ce472440ad5f5c106d33ad19821b8ca9e5d4))
* **iconfont:** 更新系统图标库 ([e30ddf1](https://git.kicad99.com/bfdx/bf8100-web/commit/e30ddf1d2cbfe3cdfcd517d9c63cf21e768ccd18))
* **iconfont:** 更新系统图标库 ([3996ce1](https://git.kicad99.com/bfdx/bf8100-web/commit/3996ce1acd41c566da7d45668af5607d6238bd93))
* **iot:** 修复更新物联终端时，没有同步更新地图marker数据问题 ([20d0497](https://git.kicad99.com/bfdx/bf8100-web/commit/20d0497b3624ec2f8f1bce99028fb0cac1964ccf))
* **iot:** 修改iot命令数据逻辑，添加最后数据更新和巡查点状态更新功能 ([7cff5c4](https://git.kicad99.com/bfdx/bf8100-web/commit/7cff5c4335a5f700d71fe9c0b59a82ee918a92e9))
* **iot:** 修改人员卡指令更新巡查点marker状态变化功能 ([5cd8358](https://git.kicad99.com/bfdx/bf8100-web/commit/5cd83588dc8ce047be632404d581ca0ec3420527))
* **iot:** 修改物联终端历史指令类型和数据类型对象映射 ([331f635](https://git.kicad99.com/bfdx/bf8100-web/commit/331f6352f888a247dbc07da63e24ca5ce6b41c78))
* **iot:** 完善工牌巡查打卡通知、日志等功能 ([998b6c3](https://git.kicad99.com/bfdx/bf8100-web/commit/998b6c3b44e11fbef5a61ad978c7fe38ed2f2e2d))
* **iot:** 更新iot相关协议 ([6e591d2](https://git.kicad99.com/bfdx/bf8100-web/commit/6e591d296ac0101fb43dae0fe1e239b359efbeb6))
* **iot:** 更新iot相关协议 ([6e8253f](https://git.kicad99.com/bfdx/bf8100-web/commit/6e8253f55c5dc8d5139976e1856ead716bb9394d))
* **iot:** 添加iot模块处理iot各类命令 ([d9e779e](https://git.kicad99.com/bfdx/bf8100-web/commit/d9e779e9d1f2fd29fb877d7f904f11a282d31d97))
* **iot:** 添加工牌紧急报警处理逻辑 ([674bd24](https://git.kicad99.com/bfdx/bf8100-web/commit/674bd24d2a2d39f4ec3ce7580c2fa0c3fa8035e6))
* **iot:** 添加烟感的指令处理方法，修改marker popup内容(添加最后指令项) ([05cdf9d](https://git.kicad99.com/bfdx/bf8100-web/commit/05cdf9df9456bbd81d72504ab08c843279725f00))
* **iot:** 添加物卡指令处理入口方法 ([554682c](https://git.kicad99.com/bfdx/bf8100-web/commit/554682c2e5ee19fe034e2162d0e38c6ac6c35890))
* **iot:** 物联终端24小时无数据，则显示橙色图标； ([32629e5](https://git.kicad99.com/bfdx/bf8100-web/commit/32629e594351c759a0cc1ffa438133236a8d959b))
* **iot device:** 创建物联网终端地图marker ([78e9ea4](https://git.kicad99.com/bfdx/bf8100-web/commit/78e9ea4e13931e5f6640c5e05fdf9839e42dab08))
* **iot device:** 添加物联网终端数据管理逻辑 ([4ad96b6](https://git.kicad99.com/bfdx/bf8100-web/commit/4ad96b6983592a0078275b833ab27fb1f3317157))
* **iot device:** 添加物联网终端管理组件 ([c0c612f](https://git.kicad99.com/bfdx/bf8100-web/commit/c0c612fabdb7e601d4f7e119133ad14121d6478b))
* **package:** upgrade package dependencies ([7229a39](https://git.kicad99.com/bfdx/bf8100-web/commit/7229a39a9749ccd4eb20d7b794e5778caee00cff))
* **package:** upgrade version to 2.21.1 ([4988cdf](https://git.kicad99.com/bfdx/bf8100-web/commit/4988cdf0647a176f809c4edc95c92b24c11c1f65))
* fix bug at iot dev form ([389c44f](https://git.kicad99.com/bfdx/bf8100-web/commit/389c44f4d93c4571c4de5607be716042c41900ca))
* impl query iot device history ([5840292](https://git.kicad99.com/bfdx/bf8100-web/commit/58402923b8467b77a53bf9f044f4595d3bafc65d))
* impl query iot device last info ([de862c9](https://git.kicad99.com/bfdx/bf8100-web/commit/de862c9d014d808aea50b978a6b0595b5382cad4))
* impl query iot device last info ([03fcdd5](https://git.kicad99.com/bfdx/bf8100-web/commit/03fcdd5b3b33005a63009d16c04492dbc2c9babc))
* **proto:** 更新协议，添加iot物联网 ([29797ac](https://git.kicad99.com/bfdx/bf8100-web/commit/29797ac9bf73375b6ad7adf4b88fb11025678325))

## [2.21.0](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.20.46...v2.21.0) (2021-02-19)


### Features

* use const for number ([0a612d1](https://git.kicad99.com/bfdx/bf8100-web/commit/0a612d1df55f655aa5715140aa99efd207b36b71))
* **CmdAgent Channel config:** impl dbDevice data edit.add cmdAgent channel config ([04c13b3](https://git.kicad99.com/bfdx/bf8100-web/commit/04c13b3236ea127edfea32358a1569c08ba9126d))
* **iot:** 添加物卡各类指令处理功能 ([47fe83e](https://git.kicad99.com/bfdx/bf8100-web/commit/47fe83e1217be56606322864a0009af45a200853))
* add iot dec his page ([aa875f5](https://git.kicad99.com/bfdx/bf8100-web/commit/aa875f505efd439f929e65b3b863928f6657ccff))
* add iot device history icon ([b6fad7d](https://git.kicad99.com/bfdx/bf8100-web/commit/b6fad7d56c8c35069a5caec2dea544364ce861ad))
* add lang transfer ([ae24baf](https://git.kicad99.com/bfdx/bf8100-web/commit/ae24baf8ce1baf600da65c4814b0fadd246b2f73))
* add lang transfer ([a8a4b21](https://git.kicad99.com/bfdx/bf8100-web/commit/a8a4b21f8af771f10a13fc9bbe1a69444d6a19aa))
* compile proto with oit dev ([1379b4d](https://git.kicad99.com/bfdx/bf8100-web/commit/1379b4d6c8ef0ef37aa3f3b0308c17387c4aac72))
* impl query iot device history ([6d4b8b4](https://git.kicad99.com/bfdx/bf8100-web/commit/6d4b8b426a480d172898ac804d31453205dc7dda))
* lint format ([e561709](https://git.kicad99.com/bfdx/bf8100-web/commit/e5617095b82787a36599c06829b46aa4af346126))
* **iot device:** 修改物联网终端marker popup内容 ([07643a5](https://git.kicad99.com/bfdx/bf8100-web/commit/07643a549ddf390d8b11a2e7d817267174441b17))
* **iot device:** 添加物联网终端地图标记显示控制设置 ([713f4e7](https://git.kicad99.com/bfdx/bf8100-web/commit/713f4e764753f79f7a8976a1bd98f58e5bb6b592))
* **iot device:** 终端管理添加物联巡查终端类型，DMRID值为5个字节的16进制ID ([09cb2d3](https://git.kicad99.com/bfdx/bf8100-web/commit/09cb2d3316040d3dad9d586bb38ca74807b4c404))
* **line-point:** point type add BaseStation Patrol Point ([d5c2050](https://git.kicad99.com/bfdx/bf8100-web/commit/d5c20504a44de418b7d882f827cc2841436f5725))


### Bug Fixes

* **agentDev:** change eslint debugger ([375ad53](https://git.kicad99.com/bfdx/bf8100-web/commit/375ad536207132469565687fbab45b0ea70c0157))
* **agentDev:** del debugger ([43145bb](https://git.kicad99.com/bfdx/bf8100-web/commit/43145bbee24e7d4f12330cc227d232cf03ccb7d4))
* **agentDev:** impl sync other client data for CmdAgent PC-Dev listen groups ([852ec67](https://git.kicad99.com/bfdx/bf8100-web/commit/852ec67475a4b31db03646cbb44d044066249c55))
* **agentDev:** listen group show noPermission unit ([17416ec](https://git.kicad99.com/bfdx/bf8100-web/commit/17416ec960d4926d4f269fd9d6157a7d1f452b8e))
* **agentDev:** query no permission unit data ([2e00c80](https://git.kicad99.com/bfdx/bf8100-web/commit/2e00c8028078b9f1cb74638f71acdfcf326eb410))
* **alarm history:** 修改解析历史数据功能，添加处理物联终端逻辑 ([ea3041a](https://git.kicad99.com/bfdx/bf8100-web/commit/ea3041a88c34cb58f051a44e7c5addfd16a7f679))
* **alarm history:** 终端条件添加物联终端选项 ([d09dba6](https://git.kicad99.com/bfdx/bf8100-web/commit/d09dba6ba9177cb5ef93eac0c34df4d177e05f16))
* **cache:** 请求静态json文件时，添加时间参数，以便浏览器不缓存资源 ([538b32a](https://git.kicad99.com/bfdx/bf8100-web/commit/538b32ab7f3e7ccc7f1625b8bf037aebfc10d51e))
* **ci:** debug semantic-release ([15f2659](https://git.kicad99.com/bfdx/bf8100-web/commit/15f2659ceb5d0e16633b38f56a254fc583f80449))
* **ci:** debug semantic-release ([2c21022](https://git.kicad99.com/bfdx/bf8100-web/commit/2c210222503d901c2beaa621dcd48a14126f4e4e))
* **ci:** semantic-release add "--dry-run" argv ([0dbbfe1](https://git.kicad99.com/bfdx/bf8100-web/commit/0dbbfe10d79ee916599431bfcf31b476a9fefcad))
* **ci:** update .gitlab-ci.yml ([0821825](https://git.kicad99.com/bfdx/bf8100-web/commit/082182536e191b76f56a74c9ffab2ef81c2705c3))
* **ci:** update .gitlab-ci.yml ([c349333](https://git.kicad99.com/bfdx/bf8100-web/commit/c34933318ba53ad173df72c905b9335d42176934))
* **ci:** update .gitlab-ci.yml ([42b9d11](https://git.kicad99.com/bfdx/bf8100-web/commit/42b9d11f2d2a1e91ddee2b571956d83bf9445b6d))
* **ci:** update ci release job ([3a5c19a](https://git.kicad99.com/bfdx/bf8100-web/commit/3a5c19ae9789e2f98e74aaf580952c536925e25e))
* **ci:** update semantic-release ([ce87943](https://git.kicad99.com/bfdx/bf8100-web/commit/ce87943b8e55189e5708925b3d9fd23dee9adf03))
* **ci:** update semantic-release ([4c92b2b](https://git.kicad99.com/bfdx/bf8100-web/commit/4c92b2b3252ccef2db8051de98ea2f03f1c7161c))
* **ci:** update semantic-release ([7d84dbb](https://git.kicad99.com/bfdx/bf8100-web/commit/7d84dbbd3c73a593624306f23ae8f80da1d4652d))
* **ci:** update semantic-release ([d4de7ef](https://git.kicad99.com/bfdx/bf8100-web/commit/d4de7efa075a587eac0916f62d9e62928a4a9686))
* **ci:** update semantic-release ([34f945c](https://git.kicad99.com/bfdx/bf8100-web/commit/34f945c643cd5caeb85984bad63f351615ad33f6))
* **ci:** update semantic-release job ([3ab94e0](https://git.kicad99.com/bfdx/bf8100-web/commit/3ab94e050e27d507abc3a55c12d4ed0cb491fcda))
* **ci:** update semantic-release job ([fe59eac](https://git.kicad99.com/bfdx/bf8100-web/commit/fe59eacce86ea7cf1b7a6a2981b5095b75ff0a83))
* **device:** merge master into current branch ([c9f80a3](https://git.kicad99.com/bfdx/bf8100-web/commit/c9f80a3d350161fc8a6325357262f0c2be9b3f6e))
* **device:** 修复终端信道管理，当前已经选择的接收组数据没有同步单位的dmrId问题 ([909a3b0](https://git.kicad99.com/bfdx/bf8100-web/commit/909a3b0817e0faf21cce8da93edd6d923be77bd7))
* **device:** 修改指挥坐席终端拷贝信道时只拷贝1信道 ([4aca1dd](https://git.kicad99.com/bfdx/bf8100-web/commit/4aca1ddd1067e003c57d44cbb0530b6c7f5974ef))
* **Emergency:** 紧急报警界面对于IOT设备处理。隐藏用户，devSelfId改为devName ([dda8ce4](https://git.kicad99.com/bfdx/bf8100-web/commit/dda8ce472440ad5f5c106d33ad19821b8ca9e5d4))
* **iconfont:** 更新系统图标库 ([e30ddf1](https://git.kicad99.com/bfdx/bf8100-web/commit/e30ddf1d2cbfe3cdfcd517d9c63cf21e768ccd18))
* **iconfont:** 更新系统图标库 ([3996ce1](https://git.kicad99.com/bfdx/bf8100-web/commit/3996ce1acd41c566da7d45668af5607d6238bd93))
* **iot:** 修复更新物联终端时，没有同步更新地图marker数据问题 ([20d0497](https://git.kicad99.com/bfdx/bf8100-web/commit/20d0497b3624ec2f8f1bce99028fb0cac1964ccf))
* **iot:** 修改iot命令数据逻辑，添加最后数据更新和巡查点状态更新功能 ([7cff5c4](https://git.kicad99.com/bfdx/bf8100-web/commit/7cff5c4335a5f700d71fe9c0b59a82ee918a92e9))
* **iot:** 修改人员卡指令更新巡查点marker状态变化功能 ([5cd8358](https://git.kicad99.com/bfdx/bf8100-web/commit/5cd83588dc8ce047be632404d581ca0ec3420527))
* **iot:** 修改物联终端历史指令类型和数据类型对象映射 ([331f635](https://git.kicad99.com/bfdx/bf8100-web/commit/331f6352f888a247dbc07da63e24ca5ce6b41c78))
* **iot:** 完善工牌巡查打卡通知、日志等功能 ([998b6c3](https://git.kicad99.com/bfdx/bf8100-web/commit/998b6c3b44e11fbef5a61ad978c7fe38ed2f2e2d))
* **iot:** 更新iot相关协议 ([6e591d2](https://git.kicad99.com/bfdx/bf8100-web/commit/6e591d296ac0101fb43dae0fe1e239b359efbeb6))
* **iot:** 更新iot相关协议 ([6e8253f](https://git.kicad99.com/bfdx/bf8100-web/commit/6e8253f55c5dc8d5139976e1856ead716bb9394d))
* **iot:** 添加iot模块处理iot各类命令 ([d9e779e](https://git.kicad99.com/bfdx/bf8100-web/commit/d9e779e9d1f2fd29fb877d7f904f11a282d31d97))
* **iot:** 添加工牌紧急报警处理逻辑 ([674bd24](https://git.kicad99.com/bfdx/bf8100-web/commit/674bd24d2a2d39f4ec3ce7580c2fa0c3fa8035e6))
* **iot:** 添加烟感的指令处理方法，修改marker popup内容(添加最后指令项) ([05cdf9d](https://git.kicad99.com/bfdx/bf8100-web/commit/05cdf9df9456bbd81d72504ab08c843279725f00))
* **iot:** 添加物卡指令处理入口方法 ([554682c](https://git.kicad99.com/bfdx/bf8100-web/commit/554682c2e5ee19fe034e2162d0e38c6ac6c35890))
* **iot:** 物联终端24小时无数据，则显示橙色图标； ([32629e5](https://git.kicad99.com/bfdx/bf8100-web/commit/32629e594351c759a0cc1ffa438133236a8d959b))
* **iot device:** 创建物联网终端地图marker ([78e9ea4](https://git.kicad99.com/bfdx/bf8100-web/commit/78e9ea4e13931e5f6640c5e05fdf9839e42dab08))
* **iot device:** 添加物联网终端数据管理逻辑 ([4ad96b6](https://git.kicad99.com/bfdx/bf8100-web/commit/4ad96b6983592a0078275b833ab27fb1f3317157))
* **iot device:** 添加物联网终端管理组件 ([c0c612f](https://git.kicad99.com/bfdx/bf8100-web/commit/c0c612fabdb7e601d4f7e119133ad14121d6478b))
* **package:** upgrade package dependencies ([7229a39](https://git.kicad99.com/bfdx/bf8100-web/commit/7229a39a9749ccd4eb20d7b794e5778caee00cff))
* **package:** upgrade version to 2.21.1 ([4988cdf](https://git.kicad99.com/bfdx/bf8100-web/commit/4988cdf0647a176f809c4edc95c92b24c11c1f65))
* fix bug at iot dev form ([389c44f](https://git.kicad99.com/bfdx/bf8100-web/commit/389c44f4d93c4571c4de5607be716042c41900ca))
* impl query iot device history ([5840292](https://git.kicad99.com/bfdx/bf8100-web/commit/58402923b8467b77a53bf9f044f4595d3bafc65d))
* impl query iot device last info ([de862c9](https://git.kicad99.com/bfdx/bf8100-web/commit/de862c9d014d808aea50b978a6b0595b5382cad4))
* impl query iot device last info ([03fcdd5](https://git.kicad99.com/bfdx/bf8100-web/commit/03fcdd5b3b33005a63009d16c04492dbc2c9babc))
* **proto:** 更新协议，添加iot物联网 ([29797ac](https://git.kicad99.com/bfdx/bf8100-web/commit/29797ac9bf73375b6ad7adf4b88fb11025678325))

## [2.21.0](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.20.46...v2.21.0) (2021-02-19)


### Features

* use const for number ([0a612d1](https://git.kicad99.com/bfdx/bf8100-web/commit/0a612d1df55f655aa5715140aa99efd207b36b71))
* **CmdAgent Channel config:** impl dbDevice data edit.add cmdAgent channel config ([04c13b3](https://git.kicad99.com/bfdx/bf8100-web/commit/04c13b3236ea127edfea32358a1569c08ba9126d))
* **iot:** 添加物卡各类指令处理功能 ([47fe83e](https://git.kicad99.com/bfdx/bf8100-web/commit/47fe83e1217be56606322864a0009af45a200853))
* add iot dec his page ([aa875f5](https://git.kicad99.com/bfdx/bf8100-web/commit/aa875f505efd439f929e65b3b863928f6657ccff))
* add iot device history icon ([b6fad7d](https://git.kicad99.com/bfdx/bf8100-web/commit/b6fad7d56c8c35069a5caec2dea544364ce861ad))
* add lang transfer ([ae24baf](https://git.kicad99.com/bfdx/bf8100-web/commit/ae24baf8ce1baf600da65c4814b0fadd246b2f73))
* add lang transfer ([a8a4b21](https://git.kicad99.com/bfdx/bf8100-web/commit/a8a4b21f8af771f10a13fc9bbe1a69444d6a19aa))
* compile proto with oit dev ([1379b4d](https://git.kicad99.com/bfdx/bf8100-web/commit/1379b4d6c8ef0ef37aa3f3b0308c17387c4aac72))
* impl query iot device history ([6d4b8b4](https://git.kicad99.com/bfdx/bf8100-web/commit/6d4b8b426a480d172898ac804d31453205dc7dda))
* lint format ([e561709](https://git.kicad99.com/bfdx/bf8100-web/commit/e5617095b82787a36599c06829b46aa4af346126))
* **iot device:** 修改物联网终端marker popup内容 ([07643a5](https://git.kicad99.com/bfdx/bf8100-web/commit/07643a549ddf390d8b11a2e7d817267174441b17))
* **iot device:** 添加物联网终端地图标记显示控制设置 ([713f4e7](https://git.kicad99.com/bfdx/bf8100-web/commit/713f4e764753f79f7a8976a1bd98f58e5bb6b592))
* **iot device:** 终端管理添加物联巡查终端类型，DMRID值为5个字节的16进制ID ([09cb2d3](https://git.kicad99.com/bfdx/bf8100-web/commit/09cb2d3316040d3dad9d586bb38ca74807b4c404))
* **line-point:** point type add BaseStation Patrol Point ([d5c2050](https://git.kicad99.com/bfdx/bf8100-web/commit/d5c20504a44de418b7d882f827cc2841436f5725))


### Bug Fixes

* **agentDev:** change eslint debugger ([375ad53](https://git.kicad99.com/bfdx/bf8100-web/commit/375ad536207132469565687fbab45b0ea70c0157))
* **agentDev:** del debugger ([43145bb](https://git.kicad99.com/bfdx/bf8100-web/commit/43145bbee24e7d4f12330cc227d232cf03ccb7d4))
* **agentDev:** impl sync other client data for CmdAgent PC-Dev listen groups ([852ec67](https://git.kicad99.com/bfdx/bf8100-web/commit/852ec67475a4b31db03646cbb44d044066249c55))
* **agentDev:** listen group show noPermission unit ([17416ec](https://git.kicad99.com/bfdx/bf8100-web/commit/17416ec960d4926d4f269fd9d6157a7d1f452b8e))
* **agentDev:** query no permission unit data ([2e00c80](https://git.kicad99.com/bfdx/bf8100-web/commit/2e00c8028078b9f1cb74638f71acdfcf326eb410))
* **alarm history:** 修改解析历史数据功能，添加处理物联终端逻辑 ([ea3041a](https://git.kicad99.com/bfdx/bf8100-web/commit/ea3041a88c34cb58f051a44e7c5addfd16a7f679))
* **alarm history:** 终端条件添加物联终端选项 ([d09dba6](https://git.kicad99.com/bfdx/bf8100-web/commit/d09dba6ba9177cb5ef93eac0c34df4d177e05f16))
* **cache:** 请求静态json文件时，添加时间参数，以便浏览器不缓存资源 ([538b32a](https://git.kicad99.com/bfdx/bf8100-web/commit/538b32ab7f3e7ccc7f1625b8bf037aebfc10d51e))
* **ci:** debug semantic-release ([15f2659](https://git.kicad99.com/bfdx/bf8100-web/commit/15f2659ceb5d0e16633b38f56a254fc583f80449))
* **ci:** debug semantic-release ([2c21022](https://git.kicad99.com/bfdx/bf8100-web/commit/2c210222503d901c2beaa621dcd48a14126f4e4e))
* **ci:** semantic-release add "--dry-run" argv ([0dbbfe1](https://git.kicad99.com/bfdx/bf8100-web/commit/0dbbfe10d79ee916599431bfcf31b476a9fefcad))
* **ci:** update .gitlab-ci.yml ([0821825](https://git.kicad99.com/bfdx/bf8100-web/commit/082182536e191b76f56a74c9ffab2ef81c2705c3))
* **ci:** update .gitlab-ci.yml ([c349333](https://git.kicad99.com/bfdx/bf8100-web/commit/c34933318ba53ad173df72c905b9335d42176934))
* **ci:** update .gitlab-ci.yml ([42b9d11](https://git.kicad99.com/bfdx/bf8100-web/commit/42b9d11f2d2a1e91ddee2b571956d83bf9445b6d))
* **ci:** update ci release job ([3a5c19a](https://git.kicad99.com/bfdx/bf8100-web/commit/3a5c19ae9789e2f98e74aaf580952c536925e25e))
* **ci:** update semantic-release ([ce87943](https://git.kicad99.com/bfdx/bf8100-web/commit/ce87943b8e55189e5708925b3d9fd23dee9adf03))
* **ci:** update semantic-release ([4c92b2b](https://git.kicad99.com/bfdx/bf8100-web/commit/4c92b2b3252ccef2db8051de98ea2f03f1c7161c))
* **ci:** update semantic-release ([7d84dbb](https://git.kicad99.com/bfdx/bf8100-web/commit/7d84dbbd3c73a593624306f23ae8f80da1d4652d))
* **ci:** update semantic-release ([d4de7ef](https://git.kicad99.com/bfdx/bf8100-web/commit/d4de7efa075a587eac0916f62d9e62928a4a9686))
* **ci:** update semantic-release ([34f945c](https://git.kicad99.com/bfdx/bf8100-web/commit/34f945c643cd5caeb85984bad63f351615ad33f6))
* **ci:** update semantic-release job ([3ab94e0](https://git.kicad99.com/bfdx/bf8100-web/commit/3ab94e050e27d507abc3a55c12d4ed0cb491fcda))
* **ci:** update semantic-release job ([fe59eac](https://git.kicad99.com/bfdx/bf8100-web/commit/fe59eacce86ea7cf1b7a6a2981b5095b75ff0a83))
* **device:** merge master into current branch ([c9f80a3](https://git.kicad99.com/bfdx/bf8100-web/commit/c9f80a3d350161fc8a6325357262f0c2be9b3f6e))
* **device:** 修复终端信道管理，当前已经选择的接收组数据没有同步单位的dmrId问题 ([909a3b0](https://git.kicad99.com/bfdx/bf8100-web/commit/909a3b0817e0faf21cce8da93edd6d923be77bd7))
* **device:** 修改指挥坐席终端拷贝信道时只拷贝1信道 ([4aca1dd](https://git.kicad99.com/bfdx/bf8100-web/commit/4aca1ddd1067e003c57d44cbb0530b6c7f5974ef))
* **Emergency:** 紧急报警界面对于IOT设备处理。隐藏用户，devSelfId改为devName ([dda8ce4](https://git.kicad99.com/bfdx/bf8100-web/commit/dda8ce472440ad5f5c106d33ad19821b8ca9e5d4))
* **iconfont:** 更新系统图标库 ([e30ddf1](https://git.kicad99.com/bfdx/bf8100-web/commit/e30ddf1d2cbfe3cdfcd517d9c63cf21e768ccd18))
* **iconfont:** 更新系统图标库 ([3996ce1](https://git.kicad99.com/bfdx/bf8100-web/commit/3996ce1acd41c566da7d45668af5607d6238bd93))
* **iot:** 修复更新物联终端时，没有同步更新地图marker数据问题 ([20d0497](https://git.kicad99.com/bfdx/bf8100-web/commit/20d0497b3624ec2f8f1bce99028fb0cac1964ccf))
* **iot:** 修改iot命令数据逻辑，添加最后数据更新和巡查点状态更新功能 ([7cff5c4](https://git.kicad99.com/bfdx/bf8100-web/commit/7cff5c4335a5f700d71fe9c0b59a82ee918a92e9))
* **iot:** 修改人员卡指令更新巡查点marker状态变化功能 ([5cd8358](https://git.kicad99.com/bfdx/bf8100-web/commit/5cd83588dc8ce047be632404d581ca0ec3420527))
* **iot:** 修改物联终端历史指令类型和数据类型对象映射 ([331f635](https://git.kicad99.com/bfdx/bf8100-web/commit/331f6352f888a247dbc07da63e24ca5ce6b41c78))
* **iot:** 完善工牌巡查打卡通知、日志等功能 ([998b6c3](https://git.kicad99.com/bfdx/bf8100-web/commit/998b6c3b44e11fbef5a61ad978c7fe38ed2f2e2d))
* **iot:** 更新iot相关协议 ([6e591d2](https://git.kicad99.com/bfdx/bf8100-web/commit/6e591d296ac0101fb43dae0fe1e239b359efbeb6))
* **iot:** 更新iot相关协议 ([6e8253f](https://git.kicad99.com/bfdx/bf8100-web/commit/6e8253f55c5dc8d5139976e1856ead716bb9394d))
* **iot:** 添加iot模块处理iot各类命令 ([d9e779e](https://git.kicad99.com/bfdx/bf8100-web/commit/d9e779e9d1f2fd29fb877d7f904f11a282d31d97))
* **iot:** 添加工牌紧急报警处理逻辑 ([674bd24](https://git.kicad99.com/bfdx/bf8100-web/commit/674bd24d2a2d39f4ec3ce7580c2fa0c3fa8035e6))
* **iot:** 添加烟感的指令处理方法，修改marker popup内容(添加最后指令项) ([05cdf9d](https://git.kicad99.com/bfdx/bf8100-web/commit/05cdf9df9456bbd81d72504ab08c843279725f00))
* **iot:** 添加物卡指令处理入口方法 ([554682c](https://git.kicad99.com/bfdx/bf8100-web/commit/554682c2e5ee19fe034e2162d0e38c6ac6c35890))
* **iot:** 物联终端24小时无数据，则显示橙色图标； ([32629e5](https://git.kicad99.com/bfdx/bf8100-web/commit/32629e594351c759a0cc1ffa438133236a8d959b))
* **iot device:** 创建物联网终端地图marker ([78e9ea4](https://git.kicad99.com/bfdx/bf8100-web/commit/78e9ea4e13931e5f6640c5e05fdf9839e42dab08))
* **iot device:** 添加物联网终端数据管理逻辑 ([4ad96b6](https://git.kicad99.com/bfdx/bf8100-web/commit/4ad96b6983592a0078275b833ab27fb1f3317157))
* **iot device:** 添加物联网终端管理组件 ([c0c612f](https://git.kicad99.com/bfdx/bf8100-web/commit/c0c612fabdb7e601d4f7e119133ad14121d6478b))
* **package:** upgrade package dependencies ([7229a39](https://git.kicad99.com/bfdx/bf8100-web/commit/7229a39a9749ccd4eb20d7b794e5778caee00cff))
* **package:** upgrade version to 2.21.1 ([4988cdf](https://git.kicad99.com/bfdx/bf8100-web/commit/4988cdf0647a176f809c4edc95c92b24c11c1f65))
* fix bug at iot dev form ([389c44f](https://git.kicad99.com/bfdx/bf8100-web/commit/389c44f4d93c4571c4de5607be716042c41900ca))
* impl query iot device history ([5840292](https://git.kicad99.com/bfdx/bf8100-web/commit/58402923b8467b77a53bf9f044f4595d3bafc65d))
* impl query iot device last info ([de862c9](https://git.kicad99.com/bfdx/bf8100-web/commit/de862c9d014d808aea50b978a6b0595b5382cad4))
* impl query iot device last info ([03fcdd5](https://git.kicad99.com/bfdx/bf8100-web/commit/03fcdd5b3b33005a63009d16c04492dbc2c9babc))
* **proto:** 更新协议，添加iot物联网 ([29797ac](https://git.kicad99.com/bfdx/bf8100-web/commit/29797ac9bf73375b6ad7adf4b88fb11025678325))

### [2.20.46](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.20.45...v2.20.46) (2021-01-14)


### Bug Fixes

* **对讲机写频:** TD930SDC高级加密允许用户输入小写字母(a-f)，结束后自动转换为大写 ([4372103](https://git.kicad99.com/bfdx/bf8100-web/commit/43721039f610463b32ab99d0375ad3e62852d2bc))

### [2.20.45](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.20.44...v2.20.45) (2021-01-13)


### Bug Fixes

* **dynamic group:** 修复动态组跳转更新页时出现错误的成员数据问题 ([4a0caa9](https://git.kicad99.com/bfdx/bf8100-web/commit/4a0caa9159ee71fb42a5647b2d898d8f39c3ac4b))
* **对讲机写频:** 修改TD930SDC等机型高级加密要支持BCD码和A-F ([7ac0cda](https://git.kicad99.com/bfdx/bf8100-web/commit/7ac0cda640717662d5f6715e3646d3770ec70563))

### [2.20.44](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.20.43...v2.20.44) (2021-01-12)


### Bug Fixes

* **dynamic group:** 修改删除动态组权限判断，如果有上级单位或是自己创建的，则允许删除 ([0613910](https://git.kicad99.com/bfdx/bf8100-web/commit/06139106ae155af6c4e2a93f94172525f5946dda))

### [2.20.43](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.20.42...v2.20.43) (2021-01-12)


### Bug Fixes

* **对讲机写频:** 普通加密字符范围为[0-9a-zA-Z] ([eac2484](https://git.kicad99.com/bfdx/bf8100-web/commit/eac248471ec75d576a382f89096525ce4486e24f))

### [2.20.42](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.20.41...v2.20.42) (2021-01-12)


### Bug Fixes

* **org:** 修复添加单位后，没有订阅该单位的事件，导致系统中心无法接收新添加到该单位的终端指令 ([f63c670](https://git.kicad99.com/bfdx/bf8100-web/commit/f63c670bad4c4c128d6a31f9554cae88649b2891))
* **permission:** 用户权限添加动态组权限选项，没有该权限则无法编辑动态组 ([47f81dd](https://git.kicad99.com/bfdx/bf8100-web/commit/47f81dd56c97a174ee6947b67ba7287d843bafe2))
* **对讲机写频:** 序列号不进行大写转换 ([b33f5eb](https://git.kicad99.com/bfdx/bf8100-web/commit/b33f5eb469dfcdcf818edcdf8b92514ea6206b0e))
* **对讲机写频:** 普通加密为hex格式，区分大小写，高级加密为bcd码 ([a6485af](https://git.kicad99.com/bfdx/bf8100-web/commit/a6485af65f6284b01475068cfbb3aa583e6de5da))

### [2.20.41](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.20.40...v2.20.41) (2021-01-12)


### Bug Fixes

* **dynamic group:** 禁用动态组类型更新，成员树形过滤电话网关、中继虚拟终端 ([bb08414](https://git.kicad99.com/bfdx/bf8100-web/commit/bb084143819ee80baf686193360a28136df78973))
* **TD930 TM825:** 序列号兼容二代序列号，加密使用bcd码，修改ars长度 ([170a97a](https://git.kicad99.com/bfdx/bf8100-web/commit/170a97a70787bbbf9b55c9a0119a228c839ba294))

### [2.20.40](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.20.39...v2.20.40) (2021-01-11)


### Bug Fixes

* **dynamic group:** 修复动态组选择成员本地数据重置判断逻辑错误 ([b8ea9ae](https://git.kicad99.com/bfdx/bf8100-web/commit/b8ea9aee3cda558dbad7267b0c2356c9b5b504f1))

### [2.20.39](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.20.38...v2.20.39) (2021-01-09)


### Bug Fixes

* **dynamic Group:** 修复动态组组成员取消后重新选择时没有恢复组呼下终端成员问题 ([f326afb](https://git.kicad99.com/bfdx/bf8100-web/commit/f326afba760d592907822cd3ab89ace2f8eb285f))

### [2.20.38](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.20.37...v2.20.38) (2021-01-09)


### Bug Fixes

* **dynamic group:** 修复动态组删除时，删除对象赋值没有被vue监听导致强制删除异常问题 ([df07562](https://git.kicad99.com/bfdx/bf8100-web/commit/df07562b020218bd8ff53ed816e8c28c55507a09))

### [2.20.37](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.20.36...v2.20.37) (2021-01-09)


### Bug Fixes

* **ci:** 修改ci deploy-web job 依赖关系 ([dc9fac2](https://git.kicad99.com/bfdx/bf8100-web/commit/dc9fac2643f5376119c7ae03f8111bbd6d448a80))

### [2.20.36](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.20.35...v2.20.36) (2021-01-09)


### Bug Fixes

* **ci:** 修改ci执行流程 ([e0e4b05](https://git.kicad99.com/bfdx/bf8100-web/commit/e0e4b05a138860066cec8c5b028439d9dae2a393))
* **dynamic group:** 修复动态组更新成员时过滤操作方法异常，没有对比是否为同一类型成员问题 ([018ee9f](https://git.kicad99.com/bfdx/bf8100-web/commit/018ee9f60538dcfd70c7ec6311227e3348c39560))

### [2.20.35](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.20.34...v2.20.35) (2021-01-09)


### Bug Fixes

* **dynamic group:** 修复在存在终端情况下，选择上级组添加成员时，没有正确过滤成员操作状态问题 ([77c24d9](https://git.kicad99.com/bfdx/bf8100-web/commit/77c24d9c82e0bcaa15097a973d8889baca611b3c))
* **dynamic group:** 修改动态组详情成员选择状态同步逻辑 ([df1734a](https://git.kicad99.com/bfdx/bf8100-web/commit/df1734a0f1732f714037072a97a2602163a4b96a))

### [2.20.34](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.20.33...v2.20.34) (2021-01-05)


### Bug Fixes

* **Device:** 修复更新固定位置的终端后，没有同步更新对应的marker导致标记点不显示问题 ([6906710](https://git.kicad99.com/bfdx/bf8100-web/commit/6906710e92a5f9fa4c0dd97a6f05ca767eb9426f))
* **MapPoint:** 修复图片类型的地图点没有正确同步图片数据问题(在更新时图片被重置为默认值)， ([61b7232](https://git.kicad99.com/bfdx/bf8100-web/commit/61b7232e132ed1d1fd39a1679c8c6702b3f2558c))
* **Repeater:** 修复TR805005中继信道设置被屏蔽无法修改参数问题 ([0f037dc](https://git.kicad99.com/bfdx/bf8100-web/commit/0f037dcb203d772d3187f7ff9b2ba4e9062b0548))
* **Tree:** 修改单位节点选中逻辑，没有选中则单位则不显示对应的标记点； ([c9f031d](https://git.kicad99.com/bfdx/bf8100-web/commit/c9f031dd46f80b65c5c97c87214585a77a2ea4c7))

### [2.20.33](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.20.32...v2.20.33) (2021-01-04)


### Bug Fixes

* **Org mapPoint:** 修复单位设置地图标记点异常；修改跳转标记点方法，添加动画过程 ([aaa9fac](https://git.kicad99.com/bfdx/bf8100-web/commit/aaa9fac05d851579d7ceee55d6980c59bbf495cc))

### [2.20.32](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.20.31...v2.20.32) (2020-12-30)


### Bug Fixes

* **I18n:** 修改快速创建的动态组自动删除提示翻译 ([39512f5](https://git.kicad99.com/bfdx/bf8100-web/commit/39512f57d777a9c86fb4188a38683a7f7f61f055))

### [2.20.31](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.20.30...v2.20.31) (2020-12-30)


### Bug Fixes

* **Device channel:** 修复终端信道配置没有正确过滤不存在的接收组错误 ([1bff8e3](https://git.kicad99.com/bfdx/bf8100-web/commit/1bff8e356a3419a67654f39f0505f5195bc4f45c))
* **Repeater:** 中继写频功能兼容拥有不同功能码的同一型号中继写频 ([6df8045](https://git.kicad99.com/bfdx/bf8100-web/commit/6df8045c42bf8c686c4ea485cc8ba272587eaf9b))
* **Repeater:** 修改TR805005型号中继按键定义默认值 ([2a350f2](https://git.kicad99.com/bfdx/bf8100-web/commit/2a350f263695ad09851d5d897b66dfe5e69c85dd))
* **Repeater:** 添加新的中继型号信息 ([179e234](https://git.kicad99.com/bfdx/bf8100-web/commit/179e234cb6a94b74359e0baf786e0e2bccb66221))
* **TR805005:** 按键定义取消“恢复默认IP地址”参数 ([e27280c](https://git.kicad99.com/bfdx/bf8100-web/commit/e27280c4e989da1b41f4e760038617aa118b8c92))

### [2.20.30](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.20.29...v2.20.30) (2020-12-29)


### Bug Fixes

* **BfSpeaking:** 联网通话显示动态组成员信息 ([80929d4](https://git.kicad99.com/bfdx/bf8100-web/commit/80929d45fc3e6a0907d727a2d9e6c95908b1b148))
* **BfSpeaking:** 联网通话添加快捷创建临时组进行通话功能 ([0728298](https://git.kicad99.com/bfdx/bf8100-web/commit/0728298b9e2109d7a60d551a1c0456409d9c6c23))
* **Datatables:** 修复删除数据引发重绘子表时，因数据源已经不存在，导致生成子表错误 ([ad6cac5](https://git.kicad99.com/bfdx/bf8100-web/commit/ad6cac57f579d68187d736a56ba259080f9e18a9))
* **Dynamic group:** 修改动态组更新操作的提示信息判断 ([ec7ea80](https://git.kicad99.com/bfdx/bf8100-web/commit/ec7ea8052056f6d5434e40ed228e5c4c481941b6))
* **Dynamic group:** 动态组添加快捷临时组管理，失效后立即删除该组 ([c5fb73f](https://git.kicad99.com/bfdx/bf8100-web/commit/c5fb73f37c9e3e76e3087fdeb816ded22287c81f))
* **Dynamic group:** 动态组管理树形添加右键菜单，显示在线/全部节点 ([4f759eb](https://git.kicad99.com/bfdx/bf8100-web/commit/4f759eb64682146fe3aaf1072d8efe8e9ff88ea5))
* **Fancytree:** 修改树形节点过滤参数默认匹配全部节点 ([10b8765](https://git.kicad99.com/bfdx/bf8100-web/commit/10b8765633a4ac83481ce84e36a8ad4dfdcd53a3))
* **Fancytree mixin:** 修复删除节点的key参数处理错误 ([b1949f0](https://git.kicad99.com/bfdx/bf8100-web/commit/b1949f08943d705e7e3e80659afbb119302520b4))

### [2.20.29](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.20.28...v2.20.29) (2020-12-23)


### Bug Fixes

* **TrackCtrol:** 修复轨迹回放功能数据源在关闭窗口时被清除导致无法正常播放轨迹问题 ([700818d](https://git.kicad99.com/bfdx/bf8100-web/commit/700818dae474517ab119c4101ca8d5cf18ae3e4b))

### [2.20.28](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.20.27...v2.20.28) (2020-12-22)


### Bug Fixes

* **Dynamic group:** 如果因接收组加入的终端，在接收组成员退出后，则允许删除该终端操作 ([1a21ea0](https://git.kicad99.com/bfdx/bf8100-web/commit/1a21ea049a984f4d53f68c6e50bc1d380fc9e4b2))

### [2.20.27](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.20.26...v2.20.27) (2020-12-22)


### Bug Fixes

* **Data manager:** 删除动态组单位时，同时删除该组下成员 ([f7f50de](https://git.kicad99.com/bfdx/bf8100-web/commit/f7f50de380672158fa9f0cdbef100fb2f2ac745f))
* **Datatables:** 修改渲染子表时，defaultRender方法调用对象绑定错误 ([216ec1c](https://git.kicad99.com/bfdx/bf8100-web/commit/216ec1c2d101fe42e4d2291b81790600cbe5fdbd))
* **Dynamic group:** 修改子表渲染逻辑 ([0b5570f](https://git.kicad99.com/bfdx/bf8100-web/commit/0b5570fc5dab26fa1381f35238b6c5ae23f39911))
* **Line point:** 修改rfid前面自动补0的api错误 ([9adc901](https://git.kicad99.com/bfdx/bf8100-web/commit/9adc901aefe9bb785f1efe0c25e78cc464974987))
* **Nats:** 修复控制器、巡查点等部分组件nats主题错误 ([0a1671b](https://git.kicad99.com/bfdx/bf8100-web/commit/0a1671bba7a8472b0d8ae6112a7791e9a65af0b9))

### [2.20.26](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.20.25...v2.20.26) (2020-12-21)


### Bug Fixes

* **DataTables:** 修改数据更新后重绘数据表格逻辑，重绘的方法进行防抖处理 ([4592048](https://git.kicad99.com/bfdx/bf8100-web/commit/45920489f46469512fba22c5c282a68423128831))
* **DataTables:** 修改表格内容溢出样式处理，显示省略号 ([05ea7bf](https://git.kicad99.com/bfdx/bf8100-web/commit/05ea7bfb05f7969b43f81362b6b97f0b386d9b6f))
* **Dynamic group:** 修复1317删除成员指令没有设置"force"参数错误 ([8668163](https://git.kicad99.com/bfdx/bf8100-web/commit/866816311d90763a89461c482d5e1df1c35beb93))
* **Dynamic group:** 已失效的任务组，不能添加/删除成员 ([57e3cf4](https://git.kicad99.com/bfdx/bf8100-web/commit/57e3cf48c6a0215be1df6f264b16fe3965dc24be))
* **Nats:** 服务器响应的请求，添加是否为已经登录失效，是则强制刷新重新登录 ([eca6a0d](https://git.kicad99.com/bfdx/bf8100-web/commit/eca6a0d721d8650a8b4817507f559e218a988129))
* **System log:** 修改操作日志关于动态组信息处理逻辑 ([0fcc5cf](https://git.kicad99.com/bfdx/bf8100-web/commit/0fcc5cfd3d15edd5791a2d7c2ce72adf575097a8))

### [2.20.25](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.20.24...v2.20.25) (2020-12-18)


### Bug Fixes

* extract method ([00aa6a7](https://git.kicad99.com/bfdx/bf8100-web/commit/00aa6a7cae0873543961c83011e7c7c6eef84a93))
* impl dyGroup set member org ([cc5dbb6](https://git.kicad99.com/bfdx/bf8100-web/commit/cc5dbb68e0dd9fb85bdbcb5447efbcae10f581e2))

### [2.20.24](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.20.23...v2.20.24) (2020-12-18)


### Bug Fixes

* **CmdAgent:** 指挥坐席在线时，与手台一样显示地图标记 ([8a651db](https://git.kicad99.com/bfdx/bf8100-web/commit/8a651dbe2e46430707c1e4327bfd83bed2f38ea6))

### [2.20.23](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.20.22...v2.20.23) (2020-12-18)


### Bug Fixes

* data manager get map point lon lat ([5cc742d](https://git.kicad99.com/bfdx/bf8100-web/commit/5cc742d2fb51359c6f804247bb614452577b2ff2))
* del notes in html template ([0b90223](https://git.kicad99.com/bfdx/bf8100-web/commit/0b90223c8ac8a7d53f42c68f6ba7161c7cf85575))
* del org lonlat ([0e00424](https://git.kicad99.com/bfdx/bf8100-web/commit/0e00424773eedf109a24fa217cf65817b29bd7e5))
* impl org add map point ([5866e7a](https://git.kicad99.com/bfdx/bf8100-web/commit/5866e7aeb5efbe112cef6ad46a70c78a88a7fd1f))
* update proto ([0de9e84](https://git.kicad99.com/bfdx/bf8100-web/commit/0de9e847cbcf9de9f129ddf26af47d5a30f6f349))

### [2.20.22](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.20.21...v2.20.22) (2020-12-17)


### Bug Fixes

* **BfSpeaking:** 修复联网通话窗口通话目标树节点初始化没有过滤虚拟单位下的节点导致选择出错问题 ([23bf4ec](https://git.kicad99.com/bfdx/bf8100-web/commit/23bf4ec2ad42705bef6cb1ffbe1f168f22e5247c))

### [2.20.21](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.20.20...v2.20.21) (2020-12-16)


### Bug Fixes

* **BfSpeaking:** 修复联网通话打开通话目标或接收组设置页面时，没有数据问题 ([2c1008f](https://git.kicad99.com/bfdx/bf8100-web/commit/2c1008f3aafc0f86692d9faeea758f77ccf787e8))

### [2.20.20](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.20.19...v2.20.20) (2020-12-16)


### Bug Fixes

* **Dynamic group:** 修复删除动态组时参数错误 ([5ddb2b6](https://git.kicad99.com/bfdx/bf8100-web/commit/5ddb2b6433f32dbf34204b760d66b8f54beacf39))

### [2.20.19](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.20.18...v2.20.19) (2020-12-16)


### Bug Fixes

* **Dynamic group:** 修复新加的删除页，删除数据时弹框提示是否强制的错误 ([e00d603](https://git.kicad99.com/bfdx/bf8100-web/commit/e00d6039d9b5df799f703719bcef468e7f918878))

### [2.20.18](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.20.17...v2.20.18) (2020-12-15)


### Bug Fixes

* **Dynamic group:** 添加删除页 ([e596e71](https://git.kicad99.com/bfdx/bf8100-web/commit/e596e719ecb5db9c6c3cdaf163f652153fe544cf))
* **Dynamic group:** 添加成员信息操作功能，快捷删除选中的节点，添加页也添加成员信息 ([7569c2f](https://git.kicad99.com/bfdx/bf8100-web/commit/7569c2fb64fe61910f66a120c4c81d8c9f84eafb))

### [2.20.17](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.20.16...v2.20.17) (2020-12-14)


### Bug Fixes

* **Device:** 修复终端信道批量复制没有车载台节点问题 ([ef7bc56](https://git.kicad99.com/bfdx/bf8100-web/commit/ef7bc564773b469dbe89fdf74161fdd4b219f77e))
* **Layout-Mobile:** 调整用户权限和头像选择组件在移动端的布局 ([c758f8a](https://git.kicad99.com/bfdx/bf8100-web/commit/c758f8ad5b2afc3e9bc4ab04a78300579293fdec))
* **TableTree:** 修复fancytree ext-grid拓展计算节点显示个数问题，在树初始化后，重新计算一次(issue[#40](https://git.kicad99.com/bfdx/bf8100-web/issues/40)) ([1c18a81](https://git.kicad99.com/bfdx/bf8100-web/commit/1c18a811e444a7842d6f76dd4697226f41c3ed26))

### [2.20.16](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.20.15...v2.20.16) (2020-12-11)


### Bug Fixes

* **layout:** 修改版本tig信息，只显示前8位。修改英文下版本布局 ([621e5ec](https://git.kicad99.com/bfdx/bf8100-web/commit/621e5ecd11f380900d9cd5ce57d4c46d70d92dd8))

### [2.20.15](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.20.14...v2.20.15) (2020-12-11)


### Bug Fixes

* **dynamic group:** 修复1331指令更新组成员状态时，没有将已应答退出的成员删除问题 ([c2b3c22](https://git.kicad99.com/bfdx/bf8100-web/commit/c2b3c22350896936a3188c6e55790994f1196ce4))
* **layout:** 修改数据管理和发送命令窗口等组件在英文下的布局。修改公司英文名称错误 ([da2effb](https://git.kicad99.com/bfdx/bf8100-web/commit/da2effb711da1751e46a7e18a0f0d01d41072547))

### [2.20.14](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.20.13...v2.20.14) (2020-12-11)


### Bug Fixes

* **datatables:** 修复datatables组件高度计算错误问题 ([123ad0c](https://git.kicad99.com/bfdx/bf8100-web/commit/123ad0ca0c22837a6581854f72c42dfa0b2de352))
* **dynamic group:** 修复更新动态组成员时，没有对状态为11的进行删除处理错误 ([a390b16](https://git.kicad99.com/bfdx/bf8100-web/commit/a390b16d43b147fe88caf381ef3eb7ee5fe4c0eb))
* **dynamic group:** 修改动态组成员显示样式，动态组数据详情成员单位添加对应的接收组名称 ([82da1b3](https://git.kicad99.com/bfdx/bf8100-web/commit/82da1b34a0fedf8739672c5f6140cc94835db4e3))

### [2.20.13](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.20.12...v2.20.13) (2020-12-10)


### Bug Fixes

* **bfSpeaking:** 修改显示接收组信息触发方式 ([7facfe5](https://git.kicad99.com/bfdx/bf8100-web/commit/7facfe5daab891e0b464fe8e1137b11c0165ef08))

### [2.20.12](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.20.11...v2.20.12) (2020-12-10)


### Bug Fixes

* **firefox-v59.x.x:** 修复火狐浏览器59版本不支持es6的flat特性，导致无法访问系统问题，主要在XP系统支持使用系统 ([3e29d34](https://git.kicad99.com/bfdx/bf8100-web/commit/3e29d34aa44f9eb886b3a32d2a1b4722ec55c0f6))

### [2.20.11](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.20.10...v2.20.11) (2020-12-08)


### Bug Fixes

* **Device tree:** 设备树右键菜单添加快速呼叫选项,对目标进行快速呼叫 ([4dad8d6](https://git.kicad99.com/bfdx/bf8100-web/commit/4dad8d60acc7de3818b92f700d378e1772809eb3))

### [2.20.10](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.20.9...v2.20.10) (2020-12-07)


### Bug Fixes

* **Dynamic group:** 创建动态组后,主动查询一次组成员的状态,可能没有收到对应的状态变更通知 ([6075341](https://git.kicad99.com/bfdx/bf8100-web/commit/6075341bfea35c333725368fb63061dab3dbdb74))

### [2.20.9](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.20.8...v2.20.9) (2020-12-07)


### Bug Fixes

* **ci:** update .gitlab-ci.yml ([40aba6f](https://git.kicad99.com/bfdx/bf8100-web/commit/40aba6f322f8d7fc899239a962dbf37aa0c4c7bd))
* **configSuccess:** 中播中继写频,中继响应服务器指令的提示改为配置成功 ([f81ef9b](https://git.kicad99.com/bfdx/bf8100-web/commit/f81ef9b92c9ad549bda728d9cd7f12868e4ccf1f))

### [2.20.8](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.20.7...v2.20.8) (2020-12-07)


### Bug Fixes

* **Simulcast:** 同播中继允许配置插话功能 ([1895af3](https://git.kicad99.com/bfdx/bf8100-web/commit/1895af3af1605a7d075c180897338802b743123f))
* **Simulcast:** 同播控制器名称变更为'BF-TS908' ([a2d31f7](https://git.kicad99.com/bfdx/bf8100-web/commit/a2d31f729efc325d92fcf2cb8c142f78d17cdd20))

### [2.20.7](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.20.6...v2.20.7) (2020-12-04)


### Bug Fixes

* **dynamic group:** 修改删除动态组提示框在移动端布局样式 ([be117f0](https://git.kicad99.com/bfdx/bf8100-web/commit/be117f07ff7df81bf23d57757be707d72b6f0a3c))
* **dynamic group:** 修改动态组成员更新逻辑 ([71de42b](https://git.kicad99.com/bfdx/bf8100-web/commit/71de42bc50e61618d9a94cefbe5d0bdaaf8d1028))
* **dynamic group:** 更新页添加当前成员信息总览 ([aefd45c](https://git.kicad99.com/bfdx/bf8100-web/commit/aefd45ce76429f96c1155c88c41b764232c6ade8))
* **speaker call:** 修复删除动态组没有同步联网通话节点数据问题 ([08d4a38](https://git.kicad99.com/bfdx/bf8100-web/commit/08d4a3887fd2199bd841d95f69750790937734dd))
* **sync data:** 过滤频繁更新用户voipSpeakInfo，导致客户端频繁提示同步消息 ([890261c](https://git.kicad99.com/bfdx/bf8100-web/commit/890261c4e04a41a78d3cc4d7531ce2c43fee1158))

### [2.20.6](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.20.5...v2.20.6) (2020-12-03)


### Bug Fixes

* **dynamic group:** 动态组更新时，如果成员没有变化，则不需要更新 ([90994b2](https://git.kicad99.com/bfdx/bf8100-web/commit/90994b2e50c0a2d001a295b39e8dd5cf0013d110))
* **dynamic group:** 取消使用Promise.allSettled方法，支持不够友好 ([f05a6f4](https://git.kicad99.com/bfdx/bf8100-web/commit/f05a6f472120eff1a9eb86fbc94ae0e197b9806f))
* **dynamic group:** 取消动态组过滤删除类指令的操作 ([c273086](https://git.kicad99.com/bfdx/bf8100-web/commit/c273086612ed2c6658bf53d181671efcfc23afaa))
* **dynamic group:** 禁用因组呼加入动态组的成员的选择功能，只能由取消对应的组成员后同步取消选择 ([a1e200f](https://git.kicad99.com/bfdx/bf8100-web/commit/a1e200fe9420157091fa0abd3f937a05a9b5ca29))
* **dynamic group tree:** 接收组下的终端要禁用，不能操作 ([145fcbe](https://git.kicad99.com/bfdx/bf8100-web/commit/145fcbec1eb871f6c2ad65cddbfa13e174d20163))
* **network call:** 修复联网通话没有同步动态组节点问题 ([4e3b201](https://git.kicad99.com/bfdx/bf8100-web/commit/4e3b201a790f4fae5dba84881aa9c88371b6ec44))

### [2.20.5](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.20.4...v2.20.5) (2020-12-01)


### Bug Fixes

* **device:** 固定位置的终端在更新页更新经纬度时，没有同步到本地数据问题 ([e3ef802](https://git.kicad99.com/bfdx/bf8100-web/commit/e3ef802a5e37f73f4db416a5dff16d0536672b52))

### [2.20.4](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.20.3...v2.20.4) (2020-12-01)


### Bug Fixes

* **device:** 终端管理信道的接收组不能选择动态组，添加过滤处理 ([d692215](https://git.kicad99.com/bfdx/bf8100-web/commit/d692215bfca3240c2392262f324742466396c38b))

### [2.20.3](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.20.2...v2.20.3) (2020-12-01)


### Bug Fixes

* **dynamic group:** 修改删除动态组提示，只有任务组才有强制删除，失效的任务组必选强制删除 ([8934437](https://git.kicad99.com/bfdx/bf8100-web/commit/8934437fb9b9b504b384142e35bc1787b1196445))

### [2.20.2](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.20.1...v2.20.2) (2020-12-01)


### Bug Fixes

* **dynamic group:** 优化删除动态组提示，可以选择强制删除操作 ([6ceda19](https://git.kicad99.com/bfdx/bf8100-web/commit/6ceda195f1f432c883dba9b247bc13edd506be83))
* **dynamic group:** 有指挥坐席的用户登录后，要应答加入任务组 ([0fe9149](https://git.kicad99.com/bfdx/bf8100-web/commit/0fe9149b1248f490e75a2d23701cde6880d28967))

### [2.20.1](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.20.0...v2.20.1) (2020-12-01)


### Bug Fixes

* **readme:** edit README.md ([595a8ea](https://git.kicad99.com/bfdx/bf8100-web/commit/595a8eaf7ff3480b66bce8834c90bdf04613fdaf))

## [2.20.0](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.19.0...v2.20.0) (2020-11-27)


### Features

* **data manage:** 数据管理类定义添加清空数据方法 ([06c37d1](https://git.kicad99.com/bfdx/bf8100-web/commit/06c37d1bd0b21d23ddfb889001b6c5fe3a666529))
* **dynamic group:** 添加动态组增、删、改等交互的api ([bafaf55](https://git.kicad99.com/bfdx/bf8100-web/commit/bafaf5524a0c12a32c14815fcf311b031eb4e5de))
* **dynamic group:** 添加动态组成员数据管理对象 ([639362e](https://git.kicad99.com/bfdx/bf8100-web/commit/639362e9bfafcdeb1abe1bf0b5dfcdafd49cb70d))
* **dynamic group:** 添加动态组管理组件，及树形节点添加对应的图标和样式 ([de63819](https://git.kicad99.com/bfdx/bf8100-web/commit/de63819c26620a6db1a92d6f5e019e15dd0fa34b))
* **dynamic group:** 添加动态组选择目标的树形公共组件 ([578156e](https://git.kicad99.com/bfdx/bf8100-web/commit/578156e3e2930c56638c212b112c3701b4767c25))
* **dynamic group:** 添加同步用户的指挥坐席优先级逻辑，以便动态组功能使用 ([19c9a6e](https://git.kicad99.com/bfdx/bf8100-web/commit/19c9a6e831f80fcba03cd66de00fca6f414bbf70))
* **dynamic group:** 添加子表渲染逻辑中; 修改查询是否能加入动态组响应处理，增加判断是否已经在本组中 ([5583055](https://git.kicad99.com/bfdx/bf8100-web/commit/55830555d4c1afe87e248af4e9fdcd64deb19979))
* **dynamic group:** 添加点击节点切换选中状态功能 ([b6a0106](https://git.kicad99.com/bfdx/bf8100-web/commit/b6a010682ce835d658239723f429d5aa2f0cbfe6))
* **proto:** 更新协议，添加动态组相关内容 ([4656e56](https://git.kicad99.com/bfdx/bf8100-web/commit/4656e56187b5d96533219da8f123b992b25fba23))


### Bug Fixes

* **bfSpeaker:** 联网通话keydown事件添加已经处理标记；打开通话目标树时，自动跳转到选择的目标节点 ([99628dc](https://git.kicad99.com/bfdx/bf8100-web/commit/99628dce197afe49ba727368650a6a9fa5dec7b3))
* **bfSpeaking:** 修复目标树节点没有过虑动态组成员错误 ([bb36fff](https://git.kicad99.com/bfdx/bf8100-web/commit/bb36fffa2445bb68ee811960e4d8206cbc222db8))
* **dataManage:** 修复单位下拉列表动态组的类型判断错误 ([8202740](https://git.kicad99.com/bfdx/bf8100-web/commit/8202740e07edb4e248d9af82a049652e465d7ea4))
* **dev:** fix dev env hostname error ([850824b](https://git.kicad99.com/bfdx/bf8100-web/commit/850824b554f889335ee6477e1a01ded93e63cbc4))
* **dmrId:** 修改生成dmrId的编号最大值限制 ([f84193d](https://git.kicad99.com/bfdx/bf8100-web/commit/f84193da84e1f8021fdefe732b6354ae84cda250))
* **dynamic group:** update dynamic group status logic, and add update fancytree node api ([a0f2982](https://git.kicad99.com/bfdx/bf8100-web/commit/a0f2982ae8bdf0dfb86cf991b413e4cbd5feeb3c))
* **dynamic group:** 修改与服务器交互的api返回的结果对象，统一为rpc对象 ([8e21753](https://git.kicad99.com/bfdx/bf8100-web/commit/8e2175394387ed3e1fd1cb0433122193f7c38322))
* **dynamic group:** 修改动态组创建布局，修改动态组增、删、改操作方法; ([e8b46cb](https://git.kicad99.com/bfdx/bf8100-web/commit/e8b46cb9c83d5c087e8e56daa77e036ab7abeba1))
* **dynamic group:** 修改动态组及成员在设备树上的显示，和同步没有权限的组成员树节点加载问题 ([4888b47](https://git.kicad99.com/bfdx/bf8100-web/commit/4888b47d7c0d364b842691e0083d261f99164fe8))
* **dynamic group:** 修改动态组及成员在设备树上的显示，和同步没有权限的组成员树节点加载问题 ([3d76a4a](https://git.kicad99.com/bfdx/bf8100-web/commit/3d76a4a3ce24e9ea3d3aa47e187b2fb0f1912836))
* **dynamic group:** 修改动态组增、删、改流程 ([1d0fdc3](https://git.kicad99.com/bfdx/bf8100-web/commit/1d0fdc32d64142d4568a5ba08a10b2eafa016c80))
* **dynamic group:** 修改动态组数据更新交互逻辑，订阅本地全局同步动态组事件 ([7c4118e](https://git.kicad99.com/bfdx/bf8100-web/commit/7c4118eba97b231333bb349f30b1c7e34face628))
* **dynamic group:** 修改动态组添加、更新操作数据结构，修改查询组或终端是否可以加入动态组参数结构，并在树节点显示已经加入的动态组组名 ([7fcc0dd](https://git.kicad99.com/bfdx/bf8100-web/commit/7fcc0dde373a48c7642b21014d8ef8a4dd31233a))
* **dynamic group:** 修改动态组添加布局 ([9558569](https://git.kicad99.com/bfdx/bf8100-web/commit/95585695e08db160e77d89fbc8b7931576c6d166))
* **dynamic group:** 修改树节点选择状态同步逻辑，提取缓存选中节点数据方法 ([c9b9785](https://git.kicad99.com/bfdx/bf8100-web/commit/c9b9785e70adba4bc90568d5b686f4fc63541058))
* **dynamic group:** 修改请求动态组数据逻辑，添加请求没有权限的组和终端数据 ([ed86a41](https://git.kicad99.com/bfdx/bf8100-web/commit/ed86a41484fd3a4a53c8b2c4149010cca16ed725))
* **dynamic group:** 将请求动态组的数据提前到请求终端数据后处理，并订阅动态组的nats事件 ([016a40d](https://git.kicad99.com/bfdx/bf8100-web/commit/016a40d35ad481e022ca006a19e9e8240af6adff))
* **dynamic group:** 更新、编译协议 ([5107898](https://git.kicad99.com/bfdx/bf8100-web/commit/510789842107affc6555cc74116240665e298e28))
* **dynamic group:** 用户登录后，查找优先级最高的指挥坐席进行调度，并通知服务器当前的优先级 ([a468126](https://git.kicad99.com/bfdx/bf8100-web/commit/a46812663b44d01d17a1da6ffd7d11e88a11f3bc))
* **dynamic group:** 调整动态组在单位数据的类型值，修改动态组状态判断 ([d7e2483](https://git.kicad99.com/bfdx/bf8100-web/commit/d7e2483688f70d48a3992f3a42656421aee4d19a))
* **dynamic group tree:** 修改处理查询是否可以加入动态组响应结果逻辑 ([c8343f4](https://git.kicad99.com/bfdx/bf8100-web/commit/c8343f42c0fa5640e5675dd1fd45658bbc4cc646))
* **fancytree:** 节点数据添加的自定义数据，在更新节点时也同步更新 ([bbc980c](https://git.kicad99.com/bfdx/bf8100-web/commit/bbc980c34b6bbd45e7cf02c63ea6efeb2973df70))
* **login:** 重新登录成功后，过滤掉之前登录超时的日志输出 ([c7c6799](https://git.kicad99.com/bfdx/bf8100-web/commit/c7c6799a692b89d473b00b5e974a3c6838d1a123))
* **proto:** 更新协议 ([f4c7371](https://git.kicad99.com/bfdx/bf8100-web/commit/f4c73717bac4c33eb415c8119654b3e2c6a4afcc))
* **rpc:** 添加beforeSend回调接口 ([17f5686](https://git.kicad99.com/bfdx/bf8100-web/commit/17f5686bbb150ab556eff961b3146b3776505ca9))
* **speaker Info:** 提取查找登录用户的通话信息方法 ([59aea61](https://git.kicad99.com/bfdx/bf8100-web/commit/59aea6127baf1c8aa2a8415059ee44937faa82da))
* **ui:** 调整dialog下的tabs标签页内边距 ([b123103](https://git.kicad99.com/bfdx/bf8100-web/commit/b12310308ca24f85666ff75047fb08ec96386496))
* **vue dircet:** 最小化，最大化/还原，添加发布自定义事件 ([413fba4](https://git.kicad99.com/bfdx/bf8100-web/commit/413fba409baf94bb4cf39370a803f7efd16f5b70))

## [2.19.0](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.18.12...v2.19.0) (2020-11-09)


### Features

* **sync center:** 同步客户端单位、设备、终端等各类数据增、删、改操作 ([de2fe4c](https://git.kicad99.com/bfdx/bf8100-web/commit/de2fe4c5a8de60d941fefa020acbc29482bf7479))
* **sync center:** 同步没有orgId字段的用户权限数据 ([cf25585](https://git.kicad99.com/bfdx/bf8100-web/commit/cf25585aa80fd4ad73e8c8ea9b862de7027a5fc2))

### [2.18.12](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.18.11...v2.18.12) (2020-11-05)


### Bug Fixes

* **webSocket:** fix read webPort err ([1cff9f2](https://git.kicad99.com/bfdx/bf8100-web/commit/1cff9f2099ceaf5b1ecac484850397bb49133662))

### [2.18.11](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.18.10...v2.18.11) (2020-11-05)


### Bug Fixes

* **webSocket:** 修改webSocket连接地址及相关配置 ([ba73ede](https://git.kicad99.com/bfdx/bf8100-web/commit/ba73eded21bbade9a16a8eff3c8812120336e0fe))

### [2.18.10](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.18.9...v2.18.10) (2020-11-04)


### Bug Fixes

* **device:** 修复组呼目标下有对讲机在线，却无法接收语音问题。添加开机、切换信道时检查信道接收组配置(组呼下对讲机没有设置接收组，服务器没有转发到对应中继) ([9f2274e](https://git.kicad99.com/bfdx/bf8100-web/commit/9f2274e966f138a5ea3006b6deb23f640e86e8d1))
* **eslint:** disable no-unused-expressions ([7d789ef](https://git.kicad99.com/bfdx/bf8100-web/commit/7d789efcdd0781f6c02276d8ce14f8a3c209329c))
* **fancytree:** 修复树形过虑匹配文本和在线终端方法时，没有兼容处理两种状态过滤问题 ([934fe82](https://git.kicad99.com/bfdx/bf8100-web/commit/934fe82ba49ae396ddbdbdf975c2c7561d73bc79))
* **fancytree:** 修复设备树右键菜单'cb类型指令名称'与发送命令窗口'指令名称'相同 ([f9fce71](https://git.kicad99.com/bfdx/bf8100-web/commit/f9fce71723274a16c38a19936c0387cb24b12fea))

### [2.18.9](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.18.8...v2.18.9) (2020-10-27)


### Bug Fixes

* **ci:** update deploy rules ([4b3f360](https://git.kicad99.com/bfdx/bf8100-web/commit/4b3f360a3004e42e26db07a9d0c2511741321d72))

### [2.18.8](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.18.7...v2.18.8) (2020-10-27)


### Bug Fixes

* **ci:** update deploy job ([bf334f6](https://git.kicad99.com/bfdx/bf8100-web/commit/bf334f6d5f54ccc90949b6a9f3f627f1dfee8c16))

### [2.18.7](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.18.6...v2.18.7) (2020-10-27)


### Bug Fixes

* **ci:** update deploy job ([230d9f7](https://git.kicad99.com/bfdx/bf8100-web/commit/230d9f72ba5c950f349ffaa15aedd04f14d2157f))

### [2.18.6](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.18.5...v2.18.6) (2020-10-26)


### Bug Fixes

* **ci:** modify release job config ([d54237b](https://git.kicad99.com/bfdx/bf8100-web/commit/d54237beb67212c3ed9a54607556209e1723ba00))

### [2.18.5](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.18.4...v2.18.5) (2020-10-26)


### Bug Fixes

* **ci:** modify release job config ([dad086c](https://git.kicad99.com/bfdx/bf8100-web/commit/dad086ce47ce4535ccfa1a590a3bb48afaf8f6ec))

### [2.18.4](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.18.3...v2.18.4) (2020-10-26)


### Bug Fixes

* **ci:** modify release job config ([86d0508](https://git.kicad99.com/bfdx/bf8100-web/commit/86d0508cf914c484df1c0de7444b1416515615d1))

### [2.18.3](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.18.2...v2.18.3) (2020-10-26)


### Bug Fixes

* **ci:** modify release job config ([17b6edb](https://git.kicad99.com/bfdx/bf8100-web/commit/17b6edb3961956732fcd5aaa52f32503a45d0019))

### [2.18.1](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.18.0...v2.18.1) (2020-10-20)


### Bug Fixes

* **ci:** update gitlab ci config ([f6c5286](https://git.kicad99.com/bfdx/bf8100-web/commit/f6c5286cc01750d6b2b097fb39bde8c477237a65))

## [2.18.0](https://git.kicad99.com/bfdx/bf8100-web/compare/v2.17.6...v2.18.0) (2020-10-20)


### Features

* **ci:** update package.json of repository ([26e1626](https://git.kicad99.com/bfdx/bf8100-web/commit/26e1626704c5fc5136b103d40cb31bb171c7d6bc))
* **ci:** 使用semantic-release自动添加tag和发布release ([ff3b16d](https://git.kicad99.com/bfdx/bf8100-web/commit/ff3b16d6d2072d5164551638f40b901810a318a2))
