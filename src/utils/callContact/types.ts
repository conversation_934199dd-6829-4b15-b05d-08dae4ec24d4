export type ContactTerminalType = 'sdcTerminal' | 'networkTerminal'
export type ContactDynamicGroupType = 'taskGroup' | 'tempGroup'
export type ContactGroupType = 'group' | ContactDynamicGroupType
export type ContactType = ContactTerminalType | ContactGroupType | 'fullCallContact'

export type CallContactEvent =
  | 'add_global_db_group_call_contacts'
  | 'update_global_db_group_call_contacts'
  | 'delete_global_db_group_call_contacts'
  | 'pupdate_global_db_group_call_contacts'
  | 'add_global_db_single_call_contacts'
  | 'update_global_db_single_call_contacts'
  | 'delete_global_db_single_call_contacts'
  | 'pupdate_global_db_single_call_contacts'
  | 'add_global_group_call_contacts'
  | 'add_global_single_call_contacts'
  | 'update_global_group_call_contacts'
  | 'update_global_single_call_contacts'
  | 'delete_global_group_call_contacts'
  | 'delete_global_single_call_contacts'

export type RecentContact = {
  // 用于RecycleScroller的key
  keyId: string
  srcRid: string
  srcName?: string
  srcType: ContactTerminalType
  targetDmrIDHex?: string
  targetRid: string
  targetName?: string
  targetType: ContactType
  // 是否为常用联系人
  isCommonContact?: boolean
  // 通话开始时间，本地时间字符串 hh:mm:ss
  soundTime?: string
  // 通话时长，单位秒
  soundLen?: number
}

export type Contact = {
  rid: string
  // 卡片联系人类型
  type: ContactType
  // 所属单位名称
  parentOrg?: string
  // 名称
  name: string
  // 联系人的Rid，OrgId或DeviceId
  targetRid: string
  dmrIDHex: string
  sortValue?: number
}

export type DynamicContact = Omit<Contact, 'type'> & {
  type: ContactDynamicGroupType
}
